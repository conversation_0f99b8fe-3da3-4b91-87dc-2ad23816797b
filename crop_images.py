import os
import random
import cv2
import numpy as np
from pathlib import Path

def create_directory(path):
    """创建目录如果不存在"""
    os.makedirs(path, exist_ok=True)

def random_crop_square(image):
    """随机裁剪图像为正方形，裁剪框大小在50-200像素之间"""
    h, w = image.shape[:2]
    
    # 随机选择裁剪大小，范围在50-200像素之间
    crop_size = random.randint(50, 200)
    
    # 确保裁剪框不会超出图像边界
    max_x = w - crop_size
    max_y = h - crop_size
    
    # 如果图像太小，无法进行指定大小的裁剪，则跳过
    if max_x < 0 or max_y < 0:
        raise ValueError("图像太小，无法进行指定大小的裁剪")
    
    # 随机选择裁剪的起始位置
    x = random.randint(0, max_x)
    y = random.randint(0, max_y)
    
    # 裁剪图像
    cropped = image[y:y+crop_size, x:x+crop_size]
    return cropped

def main():
    # 设置路径
    source_dir = "dataset/lmo/test/000002/rgb"
    output_dir = "dataset/crop_image512/others"
    
    # 创建输出目录
    create_directory(output_dir)
    
    # 获取所有图像文件
    image_files = []
    for ext in ['.jpg', '.jpeg', '.png']:
        image_files.extend(list(Path(source_dir).glob(f'*{ext}')))
    
    if not image_files:
        print("未找到图像文件！")
        return
    
    # 生成1000张裁剪后的图像
    for i in range(1000):
        # 随机选择一张图像
        image_path = random.choice(image_files)
        
        try:
            # 读取图像
            image = cv2.imread(str(image_path))
            if image is None:
                continue
                
            # 随机裁剪
            cropped = random_crop_square(image)
            
            # 调整大小到512x512
            resized = cv2.resize(cropped, (512, 512), interpolation=cv2.INTER_AREA)
            
            # 保存图像
            output_path = os.path.join(output_dir, f'crop_{i:04d}.jpg')
            cv2.imwrite(output_path, resized)
            
            if (i + 1) % 100 == 0:
                print(f"已处理 {i + 1} 张图像")
                
        except Exception as e:
            print(f"处理图像时出错: {e}")
            continue

if __name__ == "__main__":
    main() 