#%%
import os
import sys
import torch
import cv2
import numpy as np
from torchvision import transforms
from PIL import Image

# 2. 加载EfficientSAM模型
device = "cuda" if torch.cuda.is_available() else "cpu"
sam = build_efficient_sam_vitt().to(device)
sam.eval()

# 3. 输入图像路径
img_path = "/home/<USER>/projects/RJL2025/my-diff-feats-pose/dataset/linemod/LINEMOD/objects/duck/rgb/0000.jpg"  # 请替换为你的图片路径
img = cv2.imread(img_path)
if img is None:
    raise FileNotFoundError(f"找不到图片: {img_path}")
img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
img_pil = Image.fromarray(img_rgb)

# 4. 预处理
transform = transforms.Compose([
    transforms.Resize((1024, 1024)),
    transforms.ToTensor(),
])
input_tensor = transform(img_pil).unsqueeze(0).to(device)

# 5. 获取分割掩码（适配EfficientSAM推理接口）
with torch.no_grad():
    # EfficientSAM的image_encoder输出特征
    image_embeddings = sam.image_encoder(input_tensor)
    # 需要查阅EfficientSAM源码，确保mask_decoder参数顺序和数量正确
    try:
        # 获取image_pe（位置编码），如果有
        if hasattr(sam.image_encoder, "positional_encoding"):
            image_pe = sam.image_encoder.positional_encoding(input_tensor.shape[-2:], device=device)
        else:
            image_pe = None

        # 构造空prompt
        # 注意：image_embeddings的shape通常为 [B, C, H, W]，C为通道数
        # 这里假设prompt维度为 [B, 0, C]，C与image_embeddings的通道数一致
        # 但有些实现可能需要 [B, 0, prompt_dim]，请根据EfficientSAM源码调整
        if image_embeddings.ndim == 4:
            prompt_dim = image_embeddings.shape[1]
        else:
            prompt_dim = image_embeddings.shape[-1]
        sparse_prompt_embeddings = torch.zeros((input_tensor.shape[0], 0, prompt_dim), device=device)

        # multimask_output: 是否输出多mask
        multimask_output = False

        # mask decoder推理
        # 根据EfficientSAM源码，mask_decoder返回4个值：masks, iou_predictions, low_res_masks, mask_quality
        # 这里需要全部接收，否则会报“not enough values to unpack”错误
        masks, iou_predictions, low_res_masks, mask_quality = sam.mask_decoder(
            image_embeddings, image_pe, sparse_prompt_embeddings, multimask_output
        )
        # 若返回为dict，取"masks"字段
        if isinstance(masks, dict) and "masks" in masks:
            masks = masks["masks"]
    except Exception as e:
        raise RuntimeError("EfficientSAM mask decoder调用失败，请根据EfficientSAM源码调整参数。") from e

masks = masks.squeeze(0).cpu().numpy()  # [N, 1024, 1024]
if masks.ndim == 2:
    masks = masks[None, ...]  # 兼容单个mask

# 6. 创建输出文件夹
os.makedirs("./segmentation_result", exist_ok=True)

# 7. 对每个mask进行crop并保存
for idx, mask in enumerate(masks):
    mask_bin = (mask > 0.5).astype(np.uint8)
    # resize mask到原图大小
    mask_resized = cv2.resize(mask_bin, (img.shape[1], img.shape[0]), interpolation=cv2.INTER_NEAREST)
    # 黑色背景
    crop = np.zeros_like(img)
    for c in range(3):
        crop[..., c] = img[..., c] * mask_resized
    # 若只保留物体最小外接矩形区域，可用如下代码裁剪
    ys, xs = np.where(mask_resized > 0)
    if len(xs) == 0 or len(ys) == 0:
        continue  # 跳过空mask
    x_min, x_max = xs.min(), xs.max()
    y_min, y_max = ys.min(), ys.max()
    crop_obj = crop[y_min:y_max+1, x_min:x_max+1]
    # 保存
    out_path = f"./segmentation_result/object_{idx+1}.png"
    cv2.imwrite(out_path, crop_obj)

print(f"已保存所有分割物体到 ./segmentation_result 文件夹。")

#%%
import os
import cv2
import torch
import numpy as np
from PIL import Image
from torchvision import transforms
from EfficientSAM.efficient_sam.build_efficient_sam import build_efficient_sam_vitt

# === 参数设置 ===
device = torch.device("cuda:5" if torch.cuda.is_available() else "cpu")
image_path = "/home/<USER>/projects/RJL2025/my-diff-feats-pose/dataset/linemod/LINEMOD/objects/duck/rgb/0000.jpg"
output_dir = "./segmentation_result"
os.makedirs(output_dir, exist_ok=True)

# === 加载模型 ===
print("Loading model...")
model = build_efficient_sam_vitt().to(device)
model.eval()

# === 加载图像 ===
img_bgr = cv2.imread(image_path)
if img_bgr is None:
    raise FileNotFoundError(image_path)
img_rgb = cv2.cvtColor(img_bgr, cv2.COLOR_BGR2RGB)
img_pil = Image.fromarray(img_rgb)
orig_size = img_bgr.shape[:2]  # (H, W)

# === 预处理为 tensor ===
transform = transforms.Compose([
    transforms.Resize((1024, 1024)),
    transforms.ToTensor(),
])
input_tensor = transform(img_pil).unsqueeze(0).to(device)

# === 生成网格 prompt ===
grid_size = 10
H, W = 1024, 1024
step_y = H // (grid_size + 1)
step_x = W // (grid_size + 1)

points = []
for i in range(1, grid_size + 1):
    for j in range(1, grid_size + 1):
        points.append([j * step_x, i * step_y])
points = torch.tensor(points, dtype=torch.int, device=device).unsqueeze(0).unsqueeze(0)  # [1, 1, N, 2]
num_points = points.shape[2]
labels = torch.ones((1, 1, num_points), dtype=torch.int, device=device)  # [1, 1, N]
all_masks = []

with torch.no_grad():
    for pt in points[0, 0]:  # 遍历每个点
        single_point = pt.view(1, 1, 1, 2)
        single_label = torch.tensor([[[1]]], dtype=torch.int, device=device)
        pred_logits, _ = model(input_tensor, single_point, single_label)  # [1, 1, 3, H, W]

        # 提取 3 个候选 mask
        for k in range(pred_logits.shape[2]):
            mask = (pred_logits[0, 0, k] >= 0).float().cpu().numpy()
            all_masks.append(mask)


count = 0
min_area = 100

for i, mask in enumerate(all_masks):
    mask_resized = cv2.resize(mask, (orig_size[1], orig_size[0]), interpolation=cv2.INTER_NEAREST)
    mask_bin = (mask_resized > 0.5).astype(np.uint8)

    if np.sum(mask_bin) < min_area:
        continue

    ys, xs = np.where(mask_bin)
    if len(xs) == 0 or len(ys) == 0:
        continue

    x0, x1 = xs.min(), xs.max()
    y0, y1 = ys.min(), ys.max()

    crop = np.zeros_like(img_bgr)
    for c in range(3):
        crop[..., c] = img_bgr[..., c] * mask_bin
    crop_obj = crop[y0:y1+1, x0:x1+1]

    out_path = os.path.join(output_dir, f"object_{count+1}.png")
    cv2.imwrite(out_path, crop_obj)
    count += 1

print(f"✅ Saved {count} masks using per-point prompting.")


#%%
points
#%%
pred_iou