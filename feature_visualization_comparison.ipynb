#%% raw
# DINO特征可视化对比：224×224 vs 448×448

本notebook对比不同输入尺寸下DINO特征的空间分辨率和表达能力

#%%
import torch
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
from sklearn.decomposition import PCA
import cv2
from PIL import Image
import os
import glob

# 设置matplotlib中文显示
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 设置设备
device = 'cuda' if torch.cuda.is_available() else 'cpu'
print(f"使用设备: {device}")

#%%
# 导入我们的模型
import sys
sys.path.append('.')

from lib.models.new_dino_network import NewDINOExtractor
from lib.models.simple_dino_feature_network import create_feature_extractor
from lib.utils.config import Config

# 加载配置
config_run = Config('./config_run/LM_DINO_split1.json').get_config()
print("配置加载完成")

#%%
# 使用固定的图片路径
selected_image_path = "/home/<USER>/projects/RJL2025/my-diff-feats-pose/dataset/templates/linemod/test/ape/000000.png"

# 检查图片是否存在
if os.path.exists(selected_image_path):
    print(f"✅ 使用图片: {selected_image_path}")
else:
    print(f"❌ 图片不存在: {selected_image_path}")
    print("请确认路径是否正确，或手动修改 selected_image_path 变量")
    # 可以尝试其他常见路径
    alternative_paths = [
        "dataset/LINEMOD/ape/rgb/000000.jpg",
        "dataset/linemod/LINEMOD/ape/rgb/000000.jpg", 
        "data/LINEMOD/ape/rgb/000000.jpg"
    ]
    
    for alt_path in alternative_paths:
        if os.path.exists(alt_path):
            selected_image_path = alt_path
            print(f"✅ 找到替代路径: {selected_image_path}")
            break
    else:
        selected_image_path = None
        print("💡 请手动设置正确的图片路径")

#%%
def load_and_preprocess_image(image_path, target_size):
    """加载图片并预处理到指定尺寸"""
    # 读取图片
    image = cv2.imread(image_path)
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    # 转换为PIL图片并resize
    pil_image = Image.fromarray(image)
    resized_image = pil_image.resize((target_size, target_size))
    
    # 转换为tensor并标准化
    tensor_image = torch.from_numpy(np.array(resized_image)).float()
    tensor_image = tensor_image.permute(2, 0, 1)  # HWC -> CHW
    tensor_image = tensor_image / 255.0  # 归一化到[0,1]
    
    # 使用ImageNet标准化
    mean = torch.tensor([0.485, 0.456, 0.406]).view(3, 1, 1)
    std = torch.tensor([0.229, 0.224, 0.225]).view(3, 1, 1)
    tensor_image = (tensor_image - mean) / std
    
    return tensor_image.unsqueeze(0), np.array(resized_image)  # 添加batch维度

# 如果有图片路径，加载图片
if selected_image_path:
    # 加载224和448两个版本
    image_224, display_224 = load_and_preprocess_image(selected_image_path, 224)
    image_448, display_448 = load_and_preprocess_image(selected_image_path, 448)
    
    print(f"图片224版本shape: {image_224.shape}")
    print(f"图片448版本shape: {image_448.shape}")
    
    # 可视化原始图片
    fig, axes = plt.subplots(1, 2, figsize=(10, 4))
    axes[0].imshow(display_224)
    axes[0].set_title('Input Image (224×224)')
    axes[0].axis('off')
    
    axes[1].imshow(display_448)
    axes[1].set_title('Input Image (448×448)')
    axes[1].axis('off')
    
    plt.tight_layout()
    plt.show()
else:
    print("请手动设置image_path变量指向一张图片")

#%%
# 创建224版本的DINO提取器
print("创建224版本的DINO提取器...")
dino_extractor_224 = NewDINOExtractor(
    device=device,
    feature_blocks_config=config_run.model.feature_blocks,
    version=config_run.model.version,
    input_size=224
)

# 创建448版本的DINO提取器
print("创建448版本的DINO提取器...")
dino_extractor_448 = NewDINOExtractor(
    device=device,
    feature_blocks_config=config_run.model.feature_blocks,
    version=config_run.model.version,
    input_size=448
)

print("DINO提取器创建完成")
print(f"224版本空间尺寸: {dino_extractor_224.spatial_size}×{dino_extractor_224.spatial_size}")
print(f"448版本空间尺寸: {dino_extractor_448.spatial_size}×{dino_extractor_448.spatial_size}")

#%%
def extract_patch_features(extractor, image):
    """提取patch特征"""
    image = image.to(device)
    
    with torch.no_grad():
        cls_tokens, patch_tokens = extractor(image)
    
    # 取最后一层的patch tokens
    last_patch_tokens = patch_tokens[-1]  # [B, feature_dim, L]
    
    # 重塑为空间特征图
    B, C, L = last_patch_tokens.shape
    H = W = int(L ** 0.5)
    spatial_features = last_patch_tokens.view(B, C, H, W)  # [B, feature_dim, H, W]
    
    return spatial_features.squeeze(0).cpu().numpy()  # [feature_dim, H, W]

if selected_image_path:
    # 提取特征
    print("提取224版本特征...")
    features_224 = extract_patch_features(dino_extractor_224, image_224)
    
    print("提取448版本特征...")
    features_448 = extract_patch_features(dino_extractor_448, image_448)
    
    print(f"224版本特征shape: {features_224.shape}")
    print(f"448版本特征shape: {features_448.shape}")
else:
    print("跳过特征提取（无图片）")

#%%
def visualize_features_with_pca(features, title, n_components=3):
    """使用PCA可视化特征"""
    # 重塑特征: [feature_dim, H, W] -> [H*W, feature_dim]
    feature_dim, H, W = features.shape
    features_flat = features.transpose(1, 2, 0).reshape(H*W, feature_dim)
    
    # PCA降维到3维用于RGB可视化
    pca = PCA(n_components=n_components)
    features_pca = pca.fit_transform(features_flat)
    
    # 标准化到[0,1]范围
    features_pca_norm = (features_pca - features_pca.min()) / (features_pca.max() - features_pca.min())
    
    # 重塑回空间维度: [H*W, 3] -> [H, W, 3]
    features_rgb = features_pca_norm.reshape(H, W, n_components)
    
    # 可视化
    plt.figure(figsize=(8, 6))
    plt.imshow(features_rgb)
    plt.title(f'{title}\\nFeature Map Size: {H}×{W}, Explained Variance: {pca.explained_variance_ratio_[:3].sum():.3f}')
    plt.axis('off')
    
    return features_rgb, pca.explained_variance_ratio_

if selected_image_path:
    # 可视化两个版本的特征
    plt.figure(figsize=(16, 6))
    
    # 224版本
    plt.subplot(1, 2, 1)
    features_rgb_224, var_ratio_224 = visualize_features_with_pca(features_224, "DINO Features (224×224 Input, PCA Visualization)")
    
    # 448版本
    plt.subplot(1, 2, 2)
    features_rgb_448, var_ratio_448 = visualize_features_with_pca(features_448, "DINO Features (448×448 Input, PCA Visualization)")
    
    plt.tight_layout()
    plt.show()
    
    # 打印详细信息
    print("\\n=== 特征对比分析 ===")
    print(f"224×224版本:")
    print(f"  - 空间分辨率: {features_224.shape[1]}×{features_224.shape[2]}")
    print(f"  - 总patch数: {features_224.shape[1] * features_224.shape[2]}")
    print(f"  - PCA前3维解释方差比: {var_ratio_224[:3]}")
    print(f"  - PCA前3维累计解释方差: {var_ratio_224[:3].sum():.3f}")
    
    print(f"\\n448×448版本:")
    print(f"  - 空间分辨率: {features_448.shape[1]}×{features_448.shape[2]}")
    print(f"  - 总patch数: {features_448.shape[1] * features_448.shape[2]}")
    print(f"  - PCA前3维解释方差比: {var_ratio_448[:3]}")
    print(f"  - PCA前3维累计解释方差: {var_ratio_448[:3].sum():.3f}")
    
    print(f"\\n空间分辨率提升: {(features_448.shape[1] * features_448.shape[2]) / (features_224.shape[1] * features_224.shape[2]):.1f}倍")
else:
    print("跳过可视化（无图片）")

#%%
def create_detailed_comparison():
    """创建详细的对比图"""
    if not selected_image_path:
        print("无图片，跳过详细对比")
        return
        
    fig = plt.figure(figsize=(20, 12))
    
    # 原始图片
    ax1 = plt.subplot(2, 4, 1)
    plt.imshow(display_224)
    plt.title('Original Image (224×224)', fontsize=14)
    plt.axis('off')
    
    ax2 = plt.subplot(2, 4, 2)
    plt.imshow(display_448)
    plt.title('Original Image (448×448)', fontsize=14)
    plt.axis('off')
    
    # 特征图可视化
    ax3 = plt.subplot(2, 4, 3)
    plt.imshow(features_rgb_224)
    plt.title(f'DINO Features PCA Visualization\\n224→{features_224.shape[1]}×{features_224.shape[2]} patches', fontsize=14)
    plt.axis('off')
    
    ax4 = plt.subplot(2, 4, 4)
    plt.imshow(features_rgb_448)
    plt.title(f'DINO Features PCA Visualization\\n448→{features_448.shape[1]}×{features_448.shape[2]} patches', fontsize=14)
    plt.axis('off')
    
    # 特征统计对比
    ax5 = plt.subplot(2, 4, 5)
    categories = ['Spatial Res', 'Patch Count', 'PCA Variance']
    values_224 = [features_224.shape[1], features_224.shape[1]**2, var_ratio_224[:3].sum()]
    values_448 = [features_448.shape[1], features_448.shape[1]**2, var_ratio_448[:3].sum()]
    
    x = np.arange(len(categories))
    width = 0.35
    
    plt.bar(x - width/2, [values_224[0]/features_448.shape[1], values_224[1]/values_448[1], values_224[2]], 
            width, label='224×224 (relative)', alpha=0.8)
    plt.bar(x + width/2, [1.0, 1.0, values_448[2]], 
            width, label='448×448 (relative)', alpha=0.8)
    
    plt.ylabel('Relative Value')
    plt.title('Feature Statistics Comparison')
    plt.xticks(x, categories)
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # PCA方差解释对比
    ax6 = plt.subplot(2, 4, 6)
    components = np.arange(1, min(11, len(var_ratio_224)+1))
    plt.plot(components, np.cumsum(var_ratio_224[:len(components)]), 'o-', label='224×224', linewidth=2)
    plt.plot(components, np.cumsum(var_ratio_448[:len(components)]), 's-', label='448×448', linewidth=2)
    plt.xlabel('PCA Components')
    plt.ylabel('Cumulative Explained Variance')
    plt.title('PCA Variance Comparison')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 特征范数分布
    ax7 = plt.subplot(2, 4, 7)
    feature_norms_224 = np.linalg.norm(features_224.reshape(features_224.shape[0], -1), axis=0)
    feature_norms_448 = np.linalg.norm(features_448.reshape(features_448.shape[0], -1), axis=0)
    
    plt.hist(feature_norms_224, bins=30, alpha=0.7, label='224×224', density=True)
    plt.hist(feature_norms_448, bins=30, alpha=0.7, label='448×448', density=True)
    plt.xlabel('Feature Vector Norm')
    plt.ylabel('Density')
    plt.title('Feature Norm Distribution')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 空间分辨率对比可视化
    ax8 = plt.subplot(2, 4, 8)
    
    # 创建网格可视化
    grid_224 = np.zeros((features_224.shape[1], features_224.shape[2], 3))
    grid_448 = np.zeros((features_448.shape[1], features_448.shape[2], 3))
    
    # 填充颜色以显示网格
    for i in range(features_224.shape[1]):
        for j in range(features_224.shape[2]):
            grid_224[i, j] = [0.8, 0.2, 0.2] if (i+j) % 2 == 0 else [0.2, 0.2, 0.8]
    
    for i in range(features_448.shape[1]):
        for j in range(features_448.shape[2]):
            grid_448[i, j] = [0.2, 0.8, 0.2] if (i+j) % 2 == 0 else [0.8, 0.8, 0.2]
    
    # 将448的网格resize到与224相同尺寸用于对比
    grid_448_resized = cv2.resize(grid_448, (features_224.shape[2], features_224.shape[1]))
    
    # 拼接显示
    combined_grid = np.concatenate([grid_224, grid_448_resized], axis=1)
    plt.imshow(combined_grid)
    plt.title(f'Spatial Grid Comparison\\nLeft:{features_224.shape[1]}×{features_224.shape[2]} | Right:{features_448.shape[1]}×{features_448.shape[2]}')
    plt.axis('off')
    
    plt.tight_layout()
    plt.show()
    
    # 打印总结
    print("\\n" + "="*60)
    print("🎯 特征对比总结")
    print("="*60)
    print(f"📊 空间分辨率提升: {features_224.shape[1]}×{features_224.shape[2]} → {features_448.shape[1]}×{features_448.shape[2]}")
    print(f"📈 Patch数量增长: {features_224.shape[1]**2} → {features_448.shape[1]**2} ({(features_448.shape[1]**2)/(features_224.shape[1]**2):.1f}倍)")
    print(f"🔬 PCA解释方差: {var_ratio_224[:3].sum():.3f} → {var_ratio_448[:3].sum():.3f}")
    print(f"💾 内存增长估计: ~{(features_448.shape[1]**2)/(features_224.shape[1]**2):.1f}倍")
    print(f"⚡ 计算复杂度增长: ~{(features_448.shape[1]**2)/(features_224.shape[1]**2):.1f}倍")
    print("\\n✅ 448×448输入提供了更精细的空间信息，有助于pose estimation任务的精确定位！")

# 执行详细对比
create_detailed_comparison()

#%% raw
## 总结

通过这个可视化对比，我们可以看到：

### 🎯 **主要发现**
1. **空间分辨率**: 448×448输入提供了4倍更多的空间patches
2. **特征细节**: 更高分辨率捕获了更精细的空间结构
3. **计算代价**: 内存和计算量约增长4倍

### 📈 **对Pose Estimation的影响**
- **更精确定位**: 32×32的特征图比16×16提供更精细的空间信息
- **更好泛化**: 丰富的空间细节有助于在Unseen数据上的表现
- **合理代价**: 4倍的计算增长是可接受的性能提升

### 🚀 **建议**
- **推荐使用448×448**: 在硬件允许的情况下，显著的空间信息提升值得额外的计算开销
- **动态调整**: 可根据具体任务需求和硬件条件调整input_size参数
