import os
import requests
from tqdm import tqdm

def download_file(url, filename):
    """
    下载文件并显示进度条
    """
    response = requests.get(url, stream=True)
    total_size = int(response.headers.get('content-length', 0))
    
    os.makedirs(os.path.dirname(filename), exist_ok=True)
    
    with open(filename, 'wb') as f, tqdm(
        desc=filename,
        total=total_size,
        unit='iB',
        unit_scale=True,
        unit_divisor=1024,
    ) as pbar:
        for data in response.iter_content(chunk_size=1024):
            size = f.write(data)
            pbar.update(size)

if __name__ == "__main__":
    # SAM vit-h 模型的下载链接
    url = "https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth"
    save_path = "pretrained_models/sam_vit_h_4b8939.pth"
    
    print("开始下载 SAM 权重文件...")
    download_file(url, save_path)
    print(f"下载完成！文件保存在: {save_path}") 