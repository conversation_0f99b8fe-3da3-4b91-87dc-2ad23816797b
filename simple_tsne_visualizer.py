#!/usr/bin/env python3
"""
简化版t-SNE可视化脚本
直接处理保存的all_templates tensor (2709, 128, 16, 16)
"""

import os
import sys
import numpy as np
import torch
import matplotlib.pyplot as plt
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
from tqdm import tqdm

def load_tensor_file(file_path):
    """
    尝试加载不同格式的tensor文件
    """
    print(f"正在加载文件: {file_path}")
    
    try:
        # 尝试PyTorch格式
        if file_path.endswith(('.pt', '.pth')):
            data = torch.load(file_path, map_location='cpu')
            print(f"成功加载PyTorch文件")
        elif file_path.endswith('.npz'):
            data = np.load(file_path)
            print(f"成功加载NumPy文件")
        else:
            # 尝试直接用torch.load
            data = torch.load(file_path, map_location='cpu')
            print(f"成功加载文件")
            
        return data
    except Exception as e:
        print(f"加载文件失败: {e}")
        return None

def extract_templates_and_labels(data):
    """
    从加载的数据中提取templates和labels
    """
    templates = None
    labels = None
    
    if isinstance(data, torch.Tensor):
        # 直接是tensor
        templates = data
        print(f"直接tensor，形状: {templates.shape}")
    elif isinstance(data, dict):
        # 字典格式，尝试不同的key
        possible_keys = ['all_templates', 'templates', 'features', 'data']
        for key in possible_keys:
            if key in data:
                templates = data[key]
                print(f"找到templates，key: {key}, 形状: {templates.shape}")
                break
        
        # 尝试找labels
        label_keys = ['all_ids', 'labels', 'ids', 'obj_ids']
        for key in label_keys:
            if key in data:
                labels = data[key]
                print(f"找到labels，key: {key}")
                break
    elif isinstance(data, np.ndarray):
        templates = torch.from_numpy(data)
        print(f"NumPy数组转换为tensor，形状: {templates.shape}")
    
    if templates is None:
        print("错误: 无法找到templates数据")
        print(f"数据类型: {type(data)}")
        if isinstance(data, dict):
            print(f"字典keys: {list(data.keys())}")
        return None, None
    
    return templates, labels

def preprocess_features(templates):
    """
    预处理特征：将4D特征图转换为2D向量
    """
    print(f"预处理特征，输入形状: {templates.shape}")
    
    if templates.dim() == 4:
        # 4D特征图 (N, C, H, W)
        N, C, H, W = templates.shape
        
        # 方法1: 全局平均池化
        features_gap = torch.mean(templates, dim=(2, 3))  # (N, C)
        print(f"全局平均池化后形状: {features_gap.shape}")
        
        # 方法2: 展平所有特征 (可选，但会导致维度很高)
        # features_flat = templates.view(N, -1)  # (N, C*H*W)
        
        return features_gap.numpy()
    
    elif templates.dim() == 2:
        # 已经是2D特征
        print(f"输入已经是2D特征: {templates.shape}")
        return templates.numpy()
    
    else:
        print(f"不支持的特征维度: {templates.dim()}")
        return None

def create_default_labels(n_samples, n_classes=13):
    """
    创建默认标签（假设LINEMOD有13个物体）
    """
    samples_per_class = n_samples // n_classes
    labels = []
    
    for class_id in range(1, n_classes + 1):
        if class_id == n_classes:
            # 最后一个类别包含剩余样本
            remaining = n_samples - len(labels)
            labels.extend([class_id] * remaining)
        else:
            labels.extend([class_id] * samples_per_class)
    
    return np.array(labels)

def apply_tsne(features, perplexity=30, n_iter=1000):
    """
    应用t-SNE降维
    """
    print(f"应用t-SNE降维...")
    print(f"输入特征形状: {features.shape}")
    print(f"参数: perplexity={perplexity}, n_iter={n_iter}")
    
    # 如果特征维度太高，先用PCA降维
    if features.shape[1] > 50:
        print(f"特征维度较高({features.shape[1]})，先用PCA降维到50维")
        pca = PCA(n_components=50, random_state=42)
        features = pca.fit_transform(features)
        print(f"PCA后形状: {features.shape}")
        print(f"PCA解释方差比: {pca.explained_variance_ratio_.sum():.3f}")
    
    # 应用t-SNE
    tsne = TSNE(
        n_components=2,
        perplexity=perplexity,
        n_iter=n_iter,
        random_state=42,
        verbose=1
    )
    
    features_2d = tsne.fit_transform(features)
    print(f"t-SNE完成，输出形状: {features_2d.shape}")
    
    return features_2d

def visualize_results(features_2d, labels=None, save_path="templates_tsne.png"):
    """
    可视化t-SNE结果
    """
    plt.figure(figsize=(12, 10))
    
    if labels is not None:
        # 按类别着色
        unique_labels = np.unique(labels)
        colors = plt.cm.tab20(np.linspace(0, 1, len(unique_labels)))
        
        for i, label in enumerate(unique_labels):
            mask = labels == label
            plt.scatter(
                features_2d[mask, 0], 
                features_2d[mask, 1],
                c=[colors[i]], 
                label=f'Object {label}',
                alpha=0.7,
                s=20
            )
        
        plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        title = "Template Features t-SNE Visualization (Colored by Object ID)"
    else:
        # 单色显示
        plt.scatter(features_2d[:, 0], features_2d[:, 1], alpha=0.7, s=20)
        title = "Template Features t-SNE Visualization"
    
    plt.title(title, fontsize=16)
    plt.xlabel('t-SNE Dimension 1', fontsize=12)
    plt.ylabel('t-SNE Dimension 2', fontsize=12)
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"可视化结果保存到: {save_path}")
    
    # 显示统计信息
    print(f"\n=== 统计信息 ===")
    print(f"总样本数: {len(features_2d)}")
    print(f"特征范围: X[{features_2d[:, 0].min():.2f}, {features_2d[:, 0].max():.2f}], Y[{features_2d[:, 1].min():.2f}, {features_2d[:, 1].max():.2f}]")
    
    if labels is not None:
        unique_labels, counts = np.unique(labels, return_counts=True)
        print(f"物体类别数: {len(unique_labels)}")
        print("每个类别的样本数:")
        for label, count in zip(unique_labels, counts):
            print(f"  Object {label}: {count} samples")
    
    plt.show()

def main():
    if len(sys.argv) < 2:
        print("用法: python simple_tsne_visualizer.py <tensor_file_path> [output_path]")
        print("示例: python simple_tsne_visualizer.py all_templates.pt")
        return
    
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else "templates_tsne.png"
    
    # 检查文件是否存在
    if not os.path.exists(input_file):
        print(f"错误: 文件不存在: {input_file}")
        return
    
    # 加载数据
    data = load_tensor_file(input_file)
    if data is None:
        return
    
    # 提取templates和labels
    templates, labels = extract_templates_and_labels(data)
    if templates is None:
        return
    
    # 预处理特征
    features = preprocess_features(templates)
    if features is None:
        return
    
    # 如果没有标签，创建默认标签
    if labels is None:
        print("未找到标签，创建默认标签...")
        labels = create_default_labels(len(features))
    elif isinstance(labels, torch.Tensor):
        labels = labels.numpy()
    
    # 应用t-SNE
    features_2d = apply_tsne(features)
    
    # 可视化
    visualize_results(features_2d, labels, output_file)
    
    print(f"\n完成！结果保存在: {output_file}")

if __name__ == "__main__":
    main()
