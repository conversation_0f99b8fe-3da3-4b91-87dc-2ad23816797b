"""
LSH vs 传统余弦相似度复杂度分析脚本
特征图尺寸: (768, 16, 16)
模板库数量: 301
"""

import torch
import torch.nn.functional as F
import numpy as np
import time
from typing import Tuple, List
import matplotlib.pyplot as plt


class SimpleLSH:
    """简化的LSH实现，用于复杂度分析（纯PyTorch实现）"""

    def __init__(self, feature_dim: int, num_bits: int = 64, num_tables: int = 4):
        self.feature_dim = feature_dim
        self.num_bits = num_bits
        self.num_tables = num_tables

        # 生成随机投影矩阵
        self.random_projections = []
        for _ in range(num_tables):
            # 每个表使用不同的随机投影
            projection = torch.randn(feature_dim, num_bits)
            self.random_projections.append(projection)

        self.hash_tables = [dict() for _ in range(num_tables)]
        self.features = None
        self.is_trained = False

    def _hash_vector(self, vector: torch.Tensor, table_idx: int) -> str:
        """将向量哈希为二进制字符串"""
        projection = self.random_projections[table_idx]
        if vector.device != projection.device:
            projection = projection.to(vector.device)

        # 投影并二值化
        projected = torch.matmul(vector, projection)  # (batch_size, num_bits)
        binary_hash = (projected > 0).int()

        # 转换为字符串
        hash_strings = []
        for i in range(binary_hash.shape[0]):
            hash_str = ''.join(binary_hash[i].cpu().numpy().astype(str))
            hash_strings.append(hash_str)

        return hash_strings[0] if len(hash_strings) == 1 else hash_strings

    def build_index(self, features: torch.Tensor):
        """构建LSH索引"""
        self.features = features

        for table_idx in range(self.num_tables):
            hash_table = self.hash_tables[table_idx]

            for i, feature in enumerate(features):
                hash_key = self._hash_vector(feature.unsqueeze(0), table_idx)

                if hash_key not in hash_table:
                    hash_table[hash_key] = []
                hash_table[hash_key].append(i)

        self.is_trained = True

    def search(self, query: torch.Tensor, k: int) -> Tuple[torch.Tensor, torch.Tensor]:
        """LSH搜索"""
        if not self.is_trained:
            raise ValueError("Index not built yet")

        candidates = set()

        # 在所有哈希表中搜索
        for table_idx in range(self.num_tables):
            hash_key = self._hash_vector(query, table_idx)
            if hash_key in self.hash_tables[table_idx]:
                candidates.update(self.hash_tables[table_idx][hash_key])

        candidates = list(candidates)

        if len(candidates) == 0:
            # 如果没有找到候选，返回空结果
            return torch.tensor([]), torch.tensor([])

        # 限制候选数量
        if len(candidates) > k:
            candidates = candidates[:k]

        # 计算实际距离
        candidate_features = self.features[candidates]
        distances = torch.norm(query.unsqueeze(0) - candidate_features, dim=1)

        # 排序并返回top-k
        sorted_indices = torch.argsort(distances)
        top_k = min(k, len(candidates))

        final_indices = torch.tensor(candidates)[sorted_indices[:top_k]]
        final_distances = distances[sorted_indices[:top_k]]

        return final_distances, final_indices


def prepare_data():
    """准备测试数据"""
    print("准备测试数据...")
    
    # 特征图参数
    C, H, W = 768, 16, 16
    spatial_dim = H * W  # 256
    feature_dim = C  # 768
    num_templates = 301
    
    print(f"特征图尺寸: ({C}, {H}, {W})")
    print(f"空间维度: {spatial_dim}")
    print(f"特征维度: {feature_dim}")
    print(f"模板数量: {num_templates}")
    
    # 生成模拟数据
    torch.manual_seed(42)
    
    # 查询特征图: (1, 768, 16, 16)
    query_feature_map = torch.randn(1, C, H, W).cuda()
    
    # 模板特征图: (301, 768, 16, 16)
    template_feature_maps = torch.randn(num_templates, C, H, W).cuda()
    
    # 归一化
    query_feature_map = F.normalize(query_feature_map, p=2, dim=1)
    template_feature_maps = F.normalize(template_feature_maps, p=2, dim=1)
    
    return query_feature_map, template_feature_maps


def traditional_cosine_similarity(query_map: torch.Tensor, template_maps: torch.Tensor) -> torch.Tensor:
    """传统的余弦相似度计算"""
    # query_map: (1, 768, 16, 16)
    # template_maps: (301, 768, 16, 16)
    
    B, C, H, W = query_map.shape
    N = template_maps.shape[0]
    
    # 方法1: 逐像素计算相似度然后平均
    # 重塑为 (B, C, H*W) 和 (N, C, H*W)
    query_flat = query_map.view(B, C, -1)  # (1, 768, 256)
    template_flat = template_maps.view(N, C, -1)  # (301, 768, 256)
    
    # 扩展维度进行批量计算
    query_expanded = query_flat.unsqueeze(1)  # (1, 1, 768, 256)
    template_expanded = template_flat.unsqueeze(0)  # (1, 301, 768, 256)
    
    # 计算每个空间位置的余弦相似度
    spatial_similarities = F.cosine_similarity(
        query_expanded,  # (1, 1, 768, 256)
        template_expanded,  # (1, 301, 768, 256)
        dim=2  # 在特征维度上计算
    )  # (1, 301, 256)
    
    # 对空间维度取平均
    similarity = spatial_similarities.mean(dim=2)  # (1, 301)
    
    return similarity.squeeze(0)  # (301,)


def lsh_based_similarity(query_map: torch.Tensor, template_maps: torch.Tensor, k: int = 50) -> torch.Tensor:
    """基于LSH的相似度计算"""
    B, C, H, W = query_map.shape
    N = template_maps.shape[0]
    spatial_dim = H * W

    # 重塑为 (spatial_dim, C)
    query_spatial = query_map.view(C, -1).t()  # (256, 768)
    template_spatial = template_maps.view(N, C, -1).permute(0, 2, 1).reshape(-1, C)  # (301*256, 768)

    # 构建LSH索引
    lsh = SimpleLSH(feature_dim=C, num_bits=64)
    lsh.build_index(template_spatial)

    # 对每个查询空间位置搜索最相似的模板空间位置
    similarities = []
    for i in range(spatial_dim):
        query_point = query_spatial[i]  # (768,)
        distances, indices = lsh.search(query_point, k=k)

        # 计算精确相似度（只对候选进行）
        if len(indices) > 0:
            candidate_features = template_spatial[indices]  # (k, 768)
            sim = F.cosine_similarity(query_point.unsqueeze(0), candidate_features, dim=1)  # (k,)
            max_sim = sim.max().item()
        else:
            max_sim = 0.0
        similarities.append(max_sim)

    # 返回平均相似度
    return torch.tensor(similarities).mean()


def analyze_complexity():
    """分析计算复杂度"""
    print("\n" + "="*50)
    print("计算复杂度理论分析")
    print("="*50)
    
    C, H, W = 768, 16, 16
    spatial_dim = H * W  # 256
    N = 301  # 模板数量
    k = 50   # LSH返回的候选数量
    
    print(f"参数设置:")
    print(f"  特征维度 C = {C}")
    print(f"  空间维度 = {spatial_dim}")
    print(f"  模板数量 N = {N}")
    print(f"  LSH候选数 k = {k}")
    
    print(f"\n传统余弦相似度复杂度:")
    traditional_ops = spatial_dim * N * C  # 每个空间位置与每个模板的每个特征维度
    print(f"  主要计算: {spatial_dim} × {N} × {C} = {traditional_ops:,} 次浮点运算")
    print(f"  空间复杂度: O({N} × {C} × {spatial_dim}) = {N * C * spatial_dim:,} 个浮点数")
    
    print(f"\nLSH优化复杂度:")
    # LSH索引构建: O(N * spatial_dim * log(N * spatial_dim))
    index_build_ops = N * spatial_dim * np.log2(N * spatial_dim)
    # LSH搜索: O(spatial_dim * log(N * spatial_dim))  
    lsh_search_ops = spatial_dim * np.log2(N * spatial_dim)
    # 精确计算: O(spatial_dim * k * C)
    exact_ops = spatial_dim * k * C
    total_lsh_ops = lsh_search_ops + exact_ops
    
    print(f"  索引构建: {N} × {spatial_dim} × log₂({N * spatial_dim}) ≈ {index_build_ops:,.0f} 次运算")
    print(f"  LSH搜索: {spatial_dim} × log₂({N * spatial_dim}) ≈ {lsh_search_ops:,.0f} 次运算")
    print(f"  精确计算: {spatial_dim} × {k} × {C} = {exact_ops:,} 次浮点运算")
    print(f"  查询总计: {total_lsh_ops:,.0f} 次运算")
    print(f"  空间复杂度: O({k} × {C} × {spatial_dim}) = {k * C * spatial_dim:,} 个浮点数")
    
    print(f"\n理论加速比:")
    speedup = traditional_ops / total_lsh_ops
    memory_saving = 1 - (k * C * spatial_dim) / (N * C * spatial_dim)
    print(f"  计算加速: {speedup:.2f}x")
    print(f"  内存节省: {memory_saving:.1%}")
    
    return traditional_ops, total_lsh_ops, speedup


def benchmark_performance():
    """实际性能测试"""
    print("\n" + "="*50)
    print("实际性能测试")
    print("="*50)
    
    # 准备数据
    query_map, template_maps = prepare_data()
    
    # 预热GPU
    print("GPU预热...")
    for _ in range(5):
        _ = traditional_cosine_similarity(query_map, template_maps)
    torch.cuda.synchronize()
    
    # 测试传统方法
    print("\n测试传统余弦相似度...")
    times_traditional = []
    for i in range(10):
        torch.cuda.synchronize()
        start_time = time.time()
        
        similarity_traditional = traditional_cosine_similarity(query_map, template_maps)
        
        torch.cuda.synchronize()
        end_time = time.time()
        times_traditional.append(end_time - start_time)
        
        if i == 0:
            print(f"  第一次结果形状: {similarity_traditional.shape}")
            print(f"  相似度范围: [{similarity_traditional.min():.4f}, {similarity_traditional.max():.4f}]")
    
    avg_time_traditional = np.mean(times_traditional[2:])  # 去掉前两次
    std_time_traditional = np.std(times_traditional[2:])
    
    # 测试LSH方法（简化版本，只测试核心思想）
    print("\n测试LSH优化方法...")
    
    # 为了公平比较，我们测试一个简化的LSH流程
    # 实际中LSH的优势在更大规模的数据上更明显
    C, H, W = query_map.shape[1:]
    N = template_maps.shape[0]
    k = 50  # 候选数量
    
    # 将特征图转换为向量形式进行LSH
    query_pooled = F.adaptive_avg_pool2d(query_map, (1, 1)).squeeze()  # (768,)
    template_pooled = F.adaptive_avg_pool2d(template_maps, (1, 1)).squeeze()  # (301, 768)
    
    # 构建LSH索引
    lsh = SimpleLSH(feature_dim=C, num_bits=64)

    times_lsh = []
    for i in range(10):
        start_time = time.time()

        # LSH搜索
        if i == 0:  # 只在第一次构建索引
            lsh.build_index(template_pooled)

        distances, indices = lsh.search(query_pooled, k=k)

        # 对候选进行精确计算
        if len(indices) > 0:
            candidate_features = template_pooled[indices]
            similarity_lsh = F.cosine_similarity(
                query_pooled.unsqueeze(0),
                candidate_features,
                dim=1
            )
        else:
            similarity_lsh = torch.tensor([0.0])

        end_time = time.time()
        times_lsh.append(end_time - start_time)

        if i == 0:
            print(f"  LSH候选数量: {len(indices)}")
            if len(indices) > 0:
                print(f"  相似度范围: [{similarity_lsh.min():.4f}, {similarity_lsh.max():.4f}]")
            else:
                print(f"  未找到候选")
    
    avg_time_lsh = np.mean(times_lsh[2:])  # 去掉前两次
    std_time_lsh = np.std(times_lsh[2:])
    
    # 结果对比
    print(f"\n性能对比结果:")
    print(f"  传统方法: {avg_time_traditional*1000:.2f} ± {std_time_lsh*1000:.2f} ms")
    print(f"  LSH方法:  {avg_time_lsh*1000:.2f} ± {std_time_lsh*1000:.2f} ms")
    print(f"  实际加速比: {avg_time_traditional/avg_time_lsh:.2f}x")
    
    return avg_time_traditional, avg_time_lsh, times_traditional, times_lsh


def visualize_results(times_traditional, times_lsh):
    """可视化结果"""
    print("\n生成性能对比图...")
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    # 时间对比
    methods = ['传统余弦相似度', 'LSH优化']
    times = [np.mean(times_traditional[2:]) * 1000, np.mean(times_lsh[2:]) * 1000]
    stds = [np.std(times_traditional[2:]) * 1000, np.std(times_lsh[2:]) * 1000]
    
    bars = ax1.bar(methods, times, yerr=stds, capsize=5, 
                   color=['#ff7f0e', '#2ca02c'], alpha=0.8)
    ax1.set_ylabel('时间 (ms)')
    ax1.set_title('平均执行时间对比')
    ax1.grid(True, alpha=0.3)
    
    # 在柱状图上添加数值
    for bar, time_val in zip(bars, times):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                f'{time_val:.2f}ms', ha='center', va='bottom')
    
    # 时间序列
    iterations = range(1, len(times_traditional) + 1)
    ax2.plot(iterations, np.array(times_traditional) * 1000, 'o-', 
             label='传统方法', linewidth=2, markersize=6)
    ax2.plot(iterations, np.array(times_lsh) * 1000, 's-', 
             label='LSH方法', linewidth=2, markersize=6)
    ax2.set_xlabel('测试轮次')
    ax2.set_ylabel('时间 (ms)')
    ax2.set_title('执行时间变化趋势')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('lsh_performance_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("性能对比图已保存为 'lsh_performance_comparison.png'")


def main():
    """主函数"""
    print("LSH vs 传统余弦相似度复杂度分析")
    print("特征图尺寸: (768, 16, 16), 模板库数量: 301")
    
    # 理论复杂度分析
    traditional_ops, lsh_ops, theoretical_speedup = analyze_complexity()
    
    # 实际性能测试
    avg_time_traditional, avg_time_lsh, times_traditional, times_lsh = benchmark_performance()
    
    # 可视化结果
    visualize_results(times_traditional, times_lsh)
    
    # 总结
    print("\n" + "="*50)
    print("总结")
    print("="*50)
    print(f"理论分析:")
    print(f"  传统方法运算量: {traditional_ops:,}")
    print(f"  LSH方法运算量: {lsh_ops:,.0f}")
    print(f"  理论加速比: {theoretical_speedup:.2f}x")
    
    print(f"\n实际测试:")
    print(f"  传统方法耗时: {avg_time_traditional*1000:.2f}ms")
    print(f"  LSH方法耗时: {avg_time_lsh*1000:.2f}ms")
    print(f"  实际加速比: {avg_time_traditional/avg_time_lsh:.2f}x")
    
    print(f"\n建议:")
    if theoretical_speedup > 2:
        print("  ✓ LSH优化在理论上有显著优势，建议在大规模模板库中使用")
    else:
        print("  ⚠ 当前规模下LSH优势不明显，建议在模板数量>1000时考虑")
    
    print(f"  ✓ 随着模板库规模增长，LSH的优势会更加明显")
    print(f"  ✓ 可以考虑混合策略：小规模用传统方法，大规模用LSH")


if __name__ == "__main__":
    main()
