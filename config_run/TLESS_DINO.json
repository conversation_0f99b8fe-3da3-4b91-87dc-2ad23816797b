{"model": {"backbone": "dinov2", "version": "dinov2_vitb14_reg", "descriptor_size": 256, "output_resolution": 16, "loss": "InfoNCE", "regression_loss": false, "use_residual": false, "cls_loss_weight": 0.5, "pose_tokens": {"enabled": false, "num_tokens": 5, "use_attention": true}, "cad_feature": {"enabled": true}, "feature_blocks": {"indices": [9, 10, 11, 12]}}, "train": {"batch_size": 32, "optimizer": {"lr": 0.0001, "weight_decay": 0.0005}, "scheduler": {"gamma": 0.2, "milestones": [10, 20, 30, 40], "update_range": "epoch"}, "epochs": 20, "num_workers": 8}, "log": {"log_interval": 10, "weights": "results/weights"}, "dataset": {"image_size": 512, "use_normalize": false, "gt_bbox_known": true, "use_mask": true, "use_aug": false, "sample_path": "results/samples", "num_negatives": 10, "aug": {"center_region_ratio": 0.5, "min_mask_ratio": 0.5, "max_mask_ratio": 0.8}}, "save_prediction_path": "results/prediction"}