{"model": {"backbone": "dinov2", "version": "dinov2_vitl14_reg", "descriptor_size": 128, "input_size": 224, "loss": "InfoNCE", "regression_loss": false, "cls_loss_weight": 0.5, "cad_feature": {"_comment": "CAD特征配置", "enabled": false, "feature_dim": 32}, "efficient_sam": {"_comment": "Cross-Modal DINO+SAM模式: enabled=true + cross_modal=true → CrossModalDINO_SAM_FeatureExtractor", "enabled": true, "cross_modal": true, "model_type": "sam_vit_b_01ec64", "feature_dim": 192}, "feature_blocks": {"indices": [9, 10, 11, 12]}, "learnable_hash": {"_comment": "可学习哈希配置 - 用于提高模板匹配精度和效率", "enabled": false, "hash_bits": 64, "use_attention": true, "dropout_rate": 0.1, "quantization_weight": 0.1, "balance_weight": 0.01, "loss_weight": 0.05}, "pose_tokens": {"use_attention": false, "num_tokens": 16, "token_dim": 1024}, "output_resolution": 16, "use_residual": true, "dropout": {"enable_dropout": true, "projection": 0.2, "attention": 0.1, "ffn": 0.3, "deep_layer": 0.3, "drop_path": 0.1}}, "train": {"batch_size": 8, "num_workers": 8, "epochs": 100, "optimizer": {"lr": 0.0001, "weight_decay": 0.0001}, "scheduler": {"milestones": [30, 60, 90], "gamma": 0.1}}, "dataset": {"split": "split1", "image_size": 512, "use_normalize": false, "gt_bbox_known": true, "use_mask": true, "use_mask_pooling": false, "_mode_examples": {"_comment": "Cross-Modal模式选择示例:", "cross_modal_dino_sam": "backbone=dinov2, sam.enabled=true, sam.cross_modal=true → CrossModalDINO_SAM_FeatureExtractor", "traditional_dino_sam": "backbone=dinov2, sam.enabled=true, sam.cross_modal=false → SpatialAwareDINO_SAM_FeatureExtractor", "dino_only": "backbone=dinov2, sam.enabled=false → SpatialAwareSimplifiedDINOFeatureExtractor", "sam_only": "backbone=sam_only, sam.enabled=true → SpatialAwareSAMOnlyFeatureExtractor", "_current_mode": "当前配置为Cross-Modal DINO+SAM模式"}, "use_aug": false, "sample_path": "results/samples", "num_negatives": 10, "aug": {"center_region_ratio": 0.5, "min_mask_ratio": 0.1, "max_mask_ratio": 0.3}}, "log": {"weights": "weights", "log_interval": 100}}