{"model": {"backbone": "diffusion", "timestep": 0, "diffusion_id": "runwayml/stable-diffusion-v1-5", "descriptor_size": 16, "output_resolution": 64, "loss": "InfoNCE", "regression_loss": false}, "train": {"batch_size": 8, "optimizer": {"lr": 0.0001, "weight_decay": 0.0005}, "scheduler": {"gamma": 0.2, "milestones": [20, 50, 80, 100], "update_range": "epoch"}, "epochs": 20, "num_workers": 0}, "log": {"log_interval": 10, "weights": "results/weights"}, "dataset": {"split": "split2", "image_size": 512, "mask_size": 32, "use_augmentation": false, "sample_path": "results/samples"}}