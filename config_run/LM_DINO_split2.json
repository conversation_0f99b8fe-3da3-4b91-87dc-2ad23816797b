{"model": {"backbone": "dinov2", "version": "dinov2_vitb14_reg", "descriptor_size": 256, "output_resolution": 16, "loss": "InfoNCE", "regression_loss": false, "use_residual": false, "pose_tokens": {"enabled": false, "num_tokens": 5, "use_attention": true}, "feature_blocks": {"indices": [9, 10, 11, 12]}}, "train": {"batch_size": 32, "optimizer": {"lr": 0.0001, "weight_decay": 0.0005}, "scheduler": {"gamma": 0.2, "milestones": [10, 20, 30, 40], "update_range": "epoch", "warmup_epochs": 5, "min_lr": 1e-06}, "epochs": 50, "num_workers": 8}, "log": {"log_interval": 10, "weights": "results/weights"}, "dataset": {"split": "split2", "image_size": 512, "mask_size": 32, "use_augmentation": false, "sample_path": "results/samples", "use_normalize": false, "gt_bbox_known": false, "use_mask": true}}