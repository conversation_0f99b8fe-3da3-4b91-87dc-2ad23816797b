{"model": {"backbone": "dinov2", "version": "dinov2_vitl14_reg", "descriptor_size": 128, "input_size": 224, "loss": "InfoNCE", "regression_loss": false, "cls_loss_weight": 0.5, "cad_feature": {"_comment": "智能模式选择: enabled=true+sam.enabled=true → 三模态(TrimodalFeatureExtractor)", "enabled": false, "feature_dim": 32}, "efficient_sam": {"_comment": "Cross-Modal模式: enabled=true+cross_modal=true → CrossModalDINO_SAM_FeatureExtractor", "enabled": false, "cross_modal": true, "model_type": "sam_vit_b_01ec64", "feature_dim": 192}, "feature_blocks": {"indices": [9, 10, 11, 12]}, "learnable_hash": {"_comment": "可学习哈希配置 - 实验1：只使用Hash特征损失，等价于wrap版本", "enabled": false, "start_epoch": 0, "hash_bits": 256, "use_attention": true, "dropout_rate": 0.1, "quantization_weight": 0.1, "balance_weight": 0.01, "loss_weight": 0.01, "_experiment1_comment": "实验1配置：只用Hash特征损失，应该等价于之前的wrap版本", "hash_task_loss_weight": 1.0, "original_task_loss_weight": 0.0}, "enable_learnable_bias": false, "offset_predictor": {"_comment": "偏移量预测器配置 - 将enabled设置为true来启用偏移量预测功能", "_usage": "启用后会在template pose基础上预测offset来精细化最终pose预测", "enabled": false, "pose_dim": 3, "loss_weight": 0.1}}, "train": {"batch_size": 16, "optimizer": {"lr": 0.0001, "weight_decay": 0.0005}, "scheduler": {"gamma": 0.2, "milestones": [10, 15, 20], "update_range": "epoch"}, "epochs": 25, "num_workers": 8}, "log": {"log_interval": 10, "weights": "results/weights"}, "dataset": {"split": "split3", "image_size": 512, "use_normalize": false, "gt_bbox_known": false, "use_mask": true, "use_mask_pooling": false, "_mode_examples": {"_comment": "模式选择示例 (通过backbone、sam.enabled和sam.cross_modal控制):", "dino_only": "backbone=dinov2, sam.enabled=false → SpatialAwareSimplifiedDINOFeatureExtractor", "dino_sam_traditional": "backbone=dinov2, sam.enabled=true, sam.cross_modal=false → SpatialAwareDINO_SAM_FeatureExtractor", "dino_sam_cross_modal": "backbone=dinov2, sam.enabled=true, sam.cross_modal=true → CrossModalDINO_SAM_FeatureExtractor", "sam_only": "backbone=sam_only, sam.enabled=true → SpatialAwareSAMOnlyFeatureExtractor", "_current_mode": "当前配置为Cross-Modal DINO+SAM模式"}, "use_aug": false, "sample_path": "results/samples", "num_negatives": 10, "aug": {"center_region_ratio": 0.5, "min_mask_ratio": 0.1, "max_mask_ratio": 0.3}}}