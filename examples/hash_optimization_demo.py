"""
Hash优化模板匹配的演示脚本
展示如何在现有的DINO特征提取系统中集成hash加速
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import numpy as np
import time
from tqdm import tqdm
import matplotlib.pyplot as plt

from lib.utils.optimized_template_matching import OptimizedTemplateMatcher
from lib.utils.config import Config
from lib.models.new_dino_network import NewDINOExtractor
from lib.datasets.linemod.dataloader_dino import LINEMODQuery, TemplatesLINEMOD


def benchmark_template_matching():
    """对比传统方法和hash优化方法的性能"""
    
    print("=== Hash优化模板匹配性能对比 ===\n")
    
    # 1. 加载配置和模型
    config_path = 'config_run/LM_DINO_split1.json'
    config_run = Config(config_path).get_config()
    
    print("1. 初始化模型...")
    model = NewDINOExtractor(config_run).cuda()
    model.eval()
    
    # 2. 准备测试数据
    print("2. 准备测试数据...")
    
    # 模拟模板特征（实际使用中从模板数据库加载）
    num_templates = 1000  # 模拟大规模模板库
    feature_dim = config_run.model.descriptor_size
    
    template_features = torch.randn(num_templates, feature_dim).cuda()
    template_features = torch.nn.functional.normalize(template_features, p=2, dim=1)
    template_ids = list(range(num_templates))
    
    # 模拟查询特征
    num_queries = 100
    query_features = torch.randn(num_queries, feature_dim).cuda()
    query_features = torch.nn.functional.normalize(query_features, p=2, dim=1)
    
    print(f"   - 模板数量: {num_templates}")
    print(f"   - 查询数量: {num_queries}")
    print(f"   - 特征维度: {feature_dim}")
    
    # 3. 传统方法测试
    print("\n3. 测试传统方法（暴力搜索）...")
    
    traditional_matcher = OptimizedTemplateMatcher(
        feature_dim=feature_dim,
        use_hash_acceleration=False,
        final_k=10,
        device='cuda'
    )
    traditional_matcher.build_template_database(template_features, template_ids)
    
    # 预热
    _ = traditional_matcher.search_templates(query_features[:5])
    traditional_matcher.reset_stats()
    
    # 正式测试
    start_time = time.time()
    traditional_results = traditional_matcher.search_templates(query_features)
    traditional_time = time.time() - start_time
    traditional_stats = traditional_matcher.get_performance_stats()
    
    print(f"   - 总耗时: {traditional_time:.3f}s")
    print(f"   - 平均每查询: {traditional_time/num_queries*1000:.2f}ms")
    
    # 4. Hash优化方法测试
    print("\n4. 测试Hash优化方法...")
    
    hash_matcher = OptimizedTemplateMatcher(
        feature_dim=feature_dim,
        use_hash_acceleration=True,
        hash_k=50,  # 预筛选50个候选
        final_k=10,
        device='cuda'
    )
    hash_matcher.build_template_database(template_features, template_ids)
    
    # 预热
    _ = hash_matcher.search_templates(query_features[:5])
    hash_matcher.reset_stats()
    
    # 正式测试
    start_time = time.time()
    hash_results = hash_matcher.search_templates(query_features)
    hash_time = time.time() - start_time
    hash_stats = hash_matcher.get_performance_stats()
    
    print(f"   - 总耗时: {hash_time:.3f}s")
    print(f"   - 平均每查询: {hash_time/num_queries*1000:.2f}ms")
    print(f"   - 理论加速比: {hash_stats['speedup_ratio']:.2f}x")
    print(f"   - 实际加速比: {traditional_time/hash_time:.2f}x")
    
    # 5. 准确性对比
    print("\n5. 准确性对比...")
    
    # 计算top-1准确性（最相似的模板是否一致）
    correct_matches = 0
    total_matches = 0
    
    for i in range(num_queries):
        if (len(traditional_results['template_ids'][i]) > 0 and 
            len(hash_results['template_ids'][i]) > 0):
            traditional_top1 = traditional_results['template_ids'][i][0]
            hash_top1 = hash_results['template_ids'][i][0]
            if traditional_top1 == hash_top1:
                correct_matches += 1
            total_matches += 1
    
    accuracy = correct_matches / total_matches if total_matches > 0 else 0
    print(f"   - Top-1一致性: {accuracy:.3f} ({correct_matches}/{total_matches})")
    
    # 6. 性能总结
    print("\n6. 性能总结:")
    print("=" * 50)
    print(f"{'方法':<15} {'总时间(s)':<10} {'平均时间(ms)':<12} {'加速比':<8}")
    print("-" * 50)
    print(f"{'传统方法':<15} {traditional_time:<10.3f} {traditional_time/num_queries*1000:<12.2f} {'1.00x':<8}")
    print(f"{'Hash优化':<15} {hash_time:<10.3f} {hash_time/num_queries*1000:<12.2f} {traditional_time/hash_time:<8.2f}x")
    print("=" * 50)
    print(f"准确性保持: {accuracy:.1%}")
    print(f"内存节省: ~{(1-hash_stats['candidates_ratio'])*100:.1f}% (候选数量减少)")
    
    return {
        'traditional_time': traditional_time,
        'hash_time': hash_time,
        'speedup': traditional_time / hash_time,
        'accuracy': accuracy,
        'traditional_stats': traditional_stats,
        'hash_stats': hash_stats
    }


def demonstrate_real_world_usage():
    """演示在真实场景中的使用方法"""
    
    print("\n\n=== 真实场景使用演示 ===\n")
    
    # 1. 加载配置
    config_path = 'config_run/LM_DINO_split1.json'
    config_run = Config(config_path).get_config()
    
    print("1. 初始化优化的模板匹配器...")
    matcher = OptimizedTemplateMatcher(
        feature_dim=config_run.model.descriptor_size,
        use_hash_acceleration=True,
        hash_k=30,  # 预筛选30个候选
        final_k=5,   # 最终返回5个最佳匹配
        similarity_threshold=0.5,  # 相似度阈值
        device='cuda'
    )
    
    # 2. 模拟构建模板数据库
    print("2. 构建模板数据库...")
    num_templates = 500
    feature_dim = config_run.model.descriptor_size
    
    # 模拟从实际模板中提取的特征
    template_features = torch.randn(num_templates, feature_dim).cuda()
    template_features = torch.nn.functional.normalize(template_features, p=2, dim=1)
    template_ids = [f"template_{i:04d}" for i in range(num_templates)]
    
    # 模拟模板姿态
    template_poses = torch.eye(4).unsqueeze(0).repeat(num_templates, 1, 1).cuda()
    for i in range(num_templates):
        # 添加随机旋转和平移
        angle = np.random.uniform(0, 2*np.pi)
        template_poses[i, :3, :3] = torch.tensor([
            [np.cos(angle), -np.sin(angle), 0],
            [np.sin(angle), np.cos(angle), 0],
            [0, 0, 1]
        ]).float()
        template_poses[i, :3, 3] = torch.randn(3) * 0.1
    
    matcher.build_template_database(
        template_features=template_features,
        template_ids=template_ids,
        template_poses=template_poses
    )
    
    # 3. 模拟查询
    print("3. 执行模板匹配查询...")
    
    # 模拟查询特征（实际中从DINO模型提取）
    query_features = torch.randn(3, feature_dim).cuda()
    query_features = torch.nn.functional.normalize(query_features, p=2, dim=1)
    
    # 执行搜索
    results = matcher.search_templates(
        query_features=query_features,
        return_poses=True
    )
    
    # 4. 显示结果
    print("4. 匹配结果:")
    for i, (similarities, template_ids_batch, poses) in enumerate(
        zip(results['similarities'], results['template_ids'], results['poses'])
    ):
        print(f"\n   查询 {i+1}:")
        print(f"   找到 {len(similarities)} 个匹配模板:")
        for j, (sim, tid, pose) in enumerate(zip(similarities, template_ids_batch, poses)):
            print(f"     {j+1}. {tid}: 相似度={sim:.3f}")
            print(f"        姿态: T=[{pose[:3,3][0]:.3f}, {pose[:3,3][1]:.3f}, {pose[:3,3][2]:.3f}]")
    
    # 5. 性能统计
    print(f"\n5. 性能统计:")
    stats = matcher.get_performance_stats()
    for key, value in stats.items():
        if isinstance(value, float):
            print(f"   {key}: {value:.3f}")
        else:
            print(f"   {key}: {value}")
    
    return results, stats


def visualize_performance_scaling():
    """可视化不同模板库大小下的性能表现"""
    
    print("\n\n=== 性能扩展性分析 ===\n")
    
    template_sizes = [100, 500, 1000, 2000, 5000]
    feature_dim = 256
    num_queries = 50
    
    traditional_times = []
    hash_times = []
    speedups = []
    
    for size in tqdm(template_sizes, desc="测试不同模板库大小"):
        # 生成测试数据
        template_features = torch.randn(size, feature_dim).cuda()
        template_features = torch.nn.functional.normalize(template_features, p=2, dim=1)
        template_ids = list(range(size))
        
        query_features = torch.randn(num_queries, feature_dim).cuda()
        query_features = torch.nn.functional.normalize(query_features, p=2, dim=1)
        
        # 测试传统方法
        traditional_matcher = OptimizedTemplateMatcher(
            feature_dim=feature_dim,
            use_hash_acceleration=False,
            device='cuda'
        )
        traditional_matcher.build_template_database(template_features, template_ids)
        
        start_time = time.time()
        _ = traditional_matcher.search_templates(query_features)
        traditional_time = time.time() - start_time
        traditional_times.append(traditional_time)
        
        # 测试hash方法
        hash_matcher = OptimizedTemplateMatcher(
            feature_dim=feature_dim,
            use_hash_acceleration=True,
            hash_k=min(50, size//10),  # 动态调整候选数量
            device='cuda'
        )
        hash_matcher.build_template_database(template_features, template_ids)
        
        start_time = time.time()
        _ = hash_matcher.search_templates(query_features)
        hash_time = time.time() - start_time
        hash_times.append(hash_time)
        
        speedups.append(traditional_time / hash_time)
    
    # 绘制结果
    plt.figure(figsize=(12, 4))
    
    plt.subplot(1, 3, 1)
    plt.plot(template_sizes, traditional_times, 'o-', label='传统方法', linewidth=2)
    plt.plot(template_sizes, hash_times, 's-', label='Hash优化', linewidth=2)
    plt.xlabel('模板库大小')
    plt.ylabel('查询时间 (秒)')
    plt.title('查询时间对比')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.subplot(1, 3, 2)
    plt.plot(template_sizes, speedups, '^-', color='green', linewidth=2)
    plt.xlabel('模板库大小')
    plt.ylabel('加速比')
    plt.title('Hash优化加速比')
    plt.grid(True, alpha=0.3)
    
    plt.subplot(1, 3, 3)
    plt.loglog(template_sizes, traditional_times, 'o-', label='传统方法')
    plt.loglog(template_sizes, hash_times, 's-', label='Hash优化')
    plt.xlabel('模板库大小 (log)')
    plt.ylabel('查询时间 (log秒)')
    plt.title('对数尺度性能对比')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('hash_optimization_performance.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("性能分析图已保存为 'hash_optimization_performance.png'")
    
    return {
        'template_sizes': template_sizes,
        'traditional_times': traditional_times,
        'hash_times': hash_times,
        'speedups': speedups
    }


if __name__ == "__main__":
    # 运行所有演示
    try:
        # 1. 性能对比
        benchmark_results = benchmark_template_matching()
        
        # 2. 真实使用演示
        demo_results, demo_stats = demonstrate_real_world_usage()
        
        # 3. 性能扩展性分析
        scaling_results = visualize_performance_scaling()
        
        print("\n\n=== 总结 ===")
        print(f"Hash优化在大规模模板匹配中可以提供 {benchmark_results['speedup']:.1f}x 的加速")
        print(f"同时保持 {benchmark_results['accuracy']:.1%} 的准确性")
        print("建议在模板库大小 > 500 时启用hash优化")
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        print("请确保已安装所需依赖: pip install faiss-gpu scikit-learn matplotlib")
