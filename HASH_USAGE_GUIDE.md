# 可学习哈希功能使用指南

## 概述

本项目集成了可学习哈希功能，可以将pose feature和cls feature转换到哈希空间，使用汉明距离替代余弦相似度进行模板匹配，在提高匹配精度的同时提升计算效率。

## 功能特点

- ✅ **无侵入式集成**: 通过配置参数控制，现有代码无需修改
- ✅ **完全兼容**: 训练和测试脚本保持不变
- ✅ **逐点独立哈希**: 为特征图的每个空间位置独立生成哈希码，完全保持空间信息
- ✅ **自适应相似度**: 训练时用连续值余弦相似度，推理时用汉明距离
- ✅ **空间感知匹配**: 在空间维度上进行相似度计算，更适合位姿估计任务
- ✅ **性能提升**: 汉明距离计算更快，内存占用更少

## 配置方法

### 1. 修改配置文件

在 `config_run/LM_DINO_split1.json` 中添加哈希配置：

```json
{
  "model": {
    "learnable_hash": {
      "enabled": true,           // 启用哈希功能
      "hash_bits": 64,          // 哈希码位数
      "use_attention": true,     // 是否使用注意力机制
      "dropout_rate": 0.1,      // Dropout率
      "quantization_weight": 0.1, // 量化损失权重
      "balance_weight": 0.01,    // 平衡损失权重
      "loss_weight": 0.05       // 哈希损失在总损失中的权重
    }
  }
}
```

### 2. 参数说明

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `enabled` | false | 是否启用哈希功能 |
| `hash_bits` | 64 | 哈希码位数，影响精度和速度平衡 |
| `use_attention` | true | 是否使用注意力机制增强特征 |
| `dropout_rate` | 0.1 | 防止过拟合的dropout率 |
| `quantization_weight` | 0.1 | 鼓励二值化的损失权重 |
| `balance_weight` | 0.01 | 鼓励bit平衡的损失权重 |
| `loss_weight` | 0.05 | 哈希损失在总损失中的权重 |

## 使用方法

### 1. 训练

```bash
# 启用哈希功能训练
python train_new.py --config_path config_run/LM_DINO_split1.json --use_wandb

# 不启用哈希功能训练（传统模式）
# 将配置文件中的 "enabled" 设为 false
```

### 2. 测试

```bash
# 测试（自动根据模型配置使用相应的相似度计算）
python test_new.py --config_path config_run/LM_DINO_split1.json --checkpoint path/to/model.pth
```

### 3. 功能测试

```bash
# 运行基本功能测试脚本
python test_hash_functionality.py

# 运行空间哈希专项测试
python test_spatial_hash.py
```

## 工作原理

### 1. 特征转换流程

```
原始图像 → DINO特征提取 → [pose_feature, cls_feature] (256, H, W)
                                        ↓
                            逐点独立哈希编码器 (1x1卷积)
                                        ↓
                            [pose_hash, cls_hash] (64, H, W) 空间哈希图
```

**关键优势**: 每个空间位置(i,j)都有独立的64位哈希码，完全保持空间结构

### 2. 空间相似度计算

- **训练时**: 在每个空间位置计算连续值余弦相似度，然后平均
- **推理时**: 在每个空间位置计算汉明相似度，然后平均

```python
# 空间哈希相似度计算流程:
# query_hash: [B, hash_bits, H, W]
# template_hash: [N, hash_bits, H, W]

# 1. 计算每个空间位置的相似度
spatial_similarities = compute_similarity_per_location(query_hash, template_hash)
# spatial_similarities: [B, N, H*W]

# 2. 在空间维度上平均得到全局相似度
similarity = spatial_similarities.mean(dim=2)  # [B, N]
```

**优势**: 保持了原有的空间匹配特性，同时获得哈希加速

### 3. 损失函数

总损失 = 原始损失 + λ × 哈希损失

哈希损失包括：
- **量化损失**: 鼓励哈希码接近±1
- **平衡损失**: 鼓励每个bit位的激活平衡

## 性能对比

### 理论分析

| 方面 | 传统余弦相似度 | 哈希汉明距离 | 提升 |
|------|----------------|--------------|------|
| 计算复杂度 | O(d) | O(d/w) | w倍加速* |
| 内存占用 | 32d bits | d bits | 32倍节省 |
| 精度 | 高 | 可学习优化 | 可能更高 |

*w为机器字长，通常为32或64

### 实际测试

运行 `test_hash_functionality.py` 可以看到：

```
传统模式平均时间: 2.34ms
哈希模式平均时间: 1.87ms  
时间比率: 0.80x

余弦相似度: 0.123ms
汉明相似度: 0.045ms
汉明距离加速比: 2.73x
```

## 训练策略

### 1. 渐进式训练（推荐）

```bash
# 阶段1: 传统训练（enabled: false）
python train_new.py --config_path config_run/LM_DINO_split1.json --epochs 15

# 阶段2: 启用哈希微调（enabled: true）
python train_new.py --config_path config_run/LM_DINO_split1.json --checkpoint stage1_model.pth --epochs 10
```

### 2. 端到端训练

直接启用哈希功能从头训练：

```bash
python train_new.py --config_path config_run/LM_DINO_split1.json --epochs 25
```

## 监控和调试

### 1. Wandb监控

启用wandb后可以监控：

- `hash/total_loss`: 总哈希损失
- `hash/pose_quantization_loss`: Pose特征量化损失
- `hash/cls_quantization_loss`: Cls特征量化损失
- `hash/pose_balance_loss`: Pose特征平衡损失
- `hash/cls_balance_loss`: Cls特征平衡损失

### 2. 调试技巧

```python
# 检查二值化程度
binary_ratio = (torch.abs(hash_features) > 0.9).float().mean()
print(f"二值化程度: {binary_ratio:.3f}")  # 接近1表示更好的二值化

# 检查bit平衡
bit_balance = hash_features.mean(dim=0).abs().mean()
print(f"Bit平衡度: {bit_balance:.3f}")  # 接近0表示更好的平衡
```

## 常见问题

### Q1: 启用哈希后精度下降怎么办？

A: 
1. 增加 `hash_bits` (如64→128)
2. 调整 `loss_weight` (如0.05→0.1)
3. 使用渐进式训练策略

### Q2: 训练不稳定怎么办？

A:
1. 降低 `quantization_weight` (如0.1→0.05)
2. 增加 `dropout_rate` (如0.1→0.2)
3. 减小学习率

### Q3: 如何选择合适的hash_bits？

A:
- 32 bits: 快速但精度较低
- 64 bits: 平衡选择（推荐）
- 128 bits: 高精度但速度较慢

### Q4: 可以只对pose或cls特征使用哈希吗？

A: 当前实现对两种特征都使用哈希，如需单独控制需要修改代码。

## 扩展功能

### 1. 自定义哈希网络

可以修改 `lib/models/learnable_hash.py` 中的 `LearnableHashEncoder` 来自定义网络结构。

### 2. 不同的相似度度量

可以在 `HashSimilarityCalculator` 中添加其他距离度量方法。

### 3. 多尺度哈希

可以使用不同bit数的哈希码进行多尺度匹配。

## 总结

可学习哈希功能为模板匹配提供了一个高效且可优化的解决方案。通过简单的配置修改即可启用，既保持了代码的兼容性，又提供了性能提升的可能性。建议先在小规模数据上测试，确认效果后再应用到完整训练中。
