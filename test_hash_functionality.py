"""
测试哈希功能的脚本
验证可学习哈希是否正确集成到现有系统中
"""

import torch
import numpy as np
from lib.utils.config import Config
from lib.models.new_dino_network import NewDINOExtractor
from lib.models.simple_dino_feature_network import create_feature_extractor


def test_hash_integration():
    """测试哈希功能集成"""
    print("=== 测试哈希功能集成 ===\n")
    
    # 1. 测试不启用哈希的情况
    print("1. 测试传统模式（不启用哈希）...")
    config_path = 'config_run/LM_DINO_split1.json'
    config = Config(config_path).get_config()
    
    # 确保哈希功能关闭
    config.model.learnable_hash.enabled = False
    
    # 创建DINO提取器
    dino_extractor = NewDINOExtractor(
        device="cuda",
        feature_blocks_config=config.model.feature_blocks,
        version=config.model.version,
        input_size=config.model.input_size
    )
    
    # 创建特征提取器
    model_traditional = create_feature_extractor(config, dino_extractor).cuda()
    model_traditional.eval()
    
    # 测试前向传播
    test_input = torch.randn(2, 3, 224, 224).cuda()
    with torch.no_grad():
        features_traditional = model_traditional(test_input)
    
    print(f"   ✓ 传统模式输出:")
    print(f"     - pose_feature shape: {features_traditional['pose_feature'].shape}")
    print(f"     - cls_feature shape: {features_traditional['cls_feature'].shape}")
    print(f"     - 是否有哈希功能: {hasattr(model_traditional, 'get_hash_loss')}")
    
    # 2. 测试启用哈希的情况
    print("\n2. 测试哈希模式（启用哈希）...")
    
    # 启用哈希功能
    config.model.learnable_hash.enabled = True
    
    # 重新创建DINO提取器
    dino_extractor_hash = NewDINOExtractor(
        device="cuda",
        feature_blocks_config=config.model.feature_blocks,
        version=config.model.version,
        input_size=config.model.input_size
    )
    
    # 创建哈希增强的特征提取器
    model_hash = create_feature_extractor(config, dino_extractor_hash).cuda()
    model_hash.eval()
    
    # 测试前向传播
    with torch.no_grad():
        features_hash = model_hash(test_input)
    
    print(f"   ✓ 哈希模式输出:")
    print(f"     - pose_feature shape: {features_hash['pose_feature'].shape}")
    print(f"     - cls_feature shape: {features_hash['cls_feature'].shape}")
    print(f"     - 是否有原始特征: {'original_pose_feature' in features_hash}")
    print(f"     - 是否有哈希功能: {hasattr(model_hash, 'get_hash_loss')}")
    
    if 'original_pose_feature' in features_hash:
        print(f"     - original_pose_feature shape: {features_hash['original_pose_feature'].shape}")
        print(f"     - original_cls_feature shape: {features_hash['original_cls_feature'].shape}")
    
    # 3. 测试相似度计算
    print("\n3. 测试相似度计算...")
    
    # 创建模板特征（模拟）
    template_features_traditional = {
        'pose_feature': torch.randn(5, config.model.descriptor_size).cuda(),
        'cls_feature': torch.randn(5, config.model.descriptor_size).cuda()
    }
    
    template_features_hash = {
        'pose_feature': torch.randn(5, config.model.learnable_hash.hash_bits).cuda(),
        'cls_feature': torch.randn(5, config.model.learnable_hash.hash_bits).cuda()
    }
    
    # 传统相似度计算
    with torch.no_grad():
        sim_traditional = model_traditional.calculate_similarity(
            features_traditional, template_features_traditional, 'pose'
        )
    
    # 哈希相似度计算
    with torch.no_grad():
        sim_hash = model_hash.calculate_similarity(
            features_hash, template_features_hash, 'pose'
        )
    
    print(f"   ✓ 传统相似度矩阵 shape: {sim_traditional.shape}")
    print(f"   ✓ 哈希相似度矩阵 shape: {sim_hash.shape}")
    print(f"   ✓ 传统相似度范围: [{sim_traditional.min():.3f}, {sim_traditional.max():.3f}]")
    print(f"   ✓ 哈希相似度范围: [{sim_hash.min():.3f}, {sim_hash.max():.3f}]")
    
    # 4. 测试训练模式下的哈希损失
    print("\n4. 测试训练模式下的哈希损失...")
    
    model_hash.train()
    
    # 前向传播（训练模式）
    features_train = model_hash(test_input)
    
    # 获取哈希损失
    hash_losses = model_hash.get_hash_loss(features_train)
    
    print(f"   ✓ 哈希损失组件:")
    for key, value in hash_losses.items():
        print(f"     - {key}: {value.item():.6f}")
    
    # 5. 测试二值化效果
    print("\n5. 测试二值化效果...")
    
    model_hash.eval()  # 推理模式，应该进行二值化
    
    with torch.no_grad():
        features_binary = model_hash(test_input)
    
    pose_hash = features_binary['pose_feature']
    cls_hash = features_binary['cls_feature']
    
    # 检查是否接近二值化
    pose_binary_ratio = ((torch.abs(pose_hash) > 0.9).float().mean()).item()
    cls_binary_ratio = ((torch.abs(cls_hash) > 0.9).float().mean()).item()
    
    print(f"   ✓ Pose特征二值化程度: {pose_binary_ratio:.3f} (接近1表示更二值化)")
    print(f"   ✓ Cls特征二值化程度: {cls_binary_ratio:.3f} (接近1表示更二值化)")
    
    # 6. 性能对比
    print("\n6. 简单性能对比...")
    
    import time
    
    # 预热
    for _ in range(10):
        with torch.no_grad():
            _ = model_traditional(test_input)
            _ = model_hash(test_input)
    
    # 测试传统模式
    torch.cuda.synchronize()
    start_time = time.time()
    for _ in range(100):
        with torch.no_grad():
            _ = model_traditional(test_input)
    torch.cuda.synchronize()
    traditional_time = time.time() - start_time
    
    # 测试哈希模式
    torch.cuda.synchronize()
    start_time = time.time()
    for _ in range(100):
        with torch.no_grad():
            _ = model_hash(test_input)
    torch.cuda.synchronize()
    hash_time = time.time() - start_time
    
    print(f"   ✓ 传统模式平均时间: {traditional_time/100*1000:.2f}ms")
    print(f"   ✓ 哈希模式平均时间: {hash_time/100*1000:.2f}ms")
    print(f"   ✓ 时间比率: {hash_time/traditional_time:.2f}x")
    
    return {
        'traditional_features': features_traditional,
        'hash_features': features_hash,
        'traditional_similarity': sim_traditional,
        'hash_similarity': sim_hash,
        'hash_losses': hash_losses,
        'binary_ratios': (pose_binary_ratio, cls_binary_ratio),
        'timing': (traditional_time, hash_time)
    }


def test_hamming_vs_cosine():
    """测试汉明距离vs余弦相似度"""
    print("\n=== 测试汉明距离 vs 余弦相似度 ===\n")
    
    from lib.models.learnable_hash import HashSimilarityCalculator
    
    # 生成测试数据
    hash_bits = 64
    batch_size = 3
    num_templates = 5
    
    # 连续值哈希码（训练时）
    query_continuous = torch.randn(batch_size, hash_bits).cuda()
    template_continuous = torch.randn(num_templates, hash_bits).cuda()
    
    # 二值化哈希码（推理时）
    query_binary = torch.sign(query_continuous)
    template_binary = torch.sign(template_continuous)
    
    calculator = HashSimilarityCalculator()
    
    # 计算不同相似度
    cosine_sim = calculator.cosine_similarity_in_hash_space(query_continuous, template_continuous)
    hamming_sim = calculator.hamming_similarity(query_binary, template_binary)
    adaptive_sim_train = calculator.adaptive_similarity(query_continuous, template_continuous, training=True)
    adaptive_sim_eval = calculator.adaptive_similarity(query_binary, template_binary, training=False)
    
    print(f"连续值余弦相似度 shape: {cosine_sim.shape}")
    print(f"汉明相似度 shape: {hamming_sim.shape}")
    print(f"自适应相似度(训练) shape: {adaptive_sim_train.shape}")
    print(f"自适应相似度(推理) shape: {adaptive_sim_eval.shape}")
    
    print(f"\n相似度范围对比:")
    print(f"余弦相似度: [{cosine_sim.min():.3f}, {cosine_sim.max():.3f}]")
    print(f"汉明相似度: [{hamming_sim.min():.3f}, {hamming_sim.max():.3f}]")
    
    # 测试计算速度
    import time
    
    # 预热
    for _ in range(100):
        _ = calculator.cosine_similarity_in_hash_space(query_continuous, template_continuous)
        _ = calculator.hamming_similarity(query_binary, template_binary)
    
    torch.cuda.synchronize()
    
    # 测试余弦相似度速度
    start_time = time.time()
    for _ in range(1000):
        _ = calculator.cosine_similarity_in_hash_space(query_continuous, template_continuous)
    torch.cuda.synchronize()
    cosine_time = time.time() - start_time
    
    # 测试汉明相似度速度
    start_time = time.time()
    for _ in range(1000):
        _ = calculator.hamming_similarity(query_binary, template_binary)
    torch.cuda.synchronize()
    hamming_time = time.time() - start_time
    
    print(f"\n速度对比:")
    print(f"余弦相似度: {cosine_time/1000*1000:.3f}ms")
    print(f"汉明相似度: {hamming_time/1000*1000:.3f}ms")
    print(f"汉明距离加速比: {cosine_time/hamming_time:.2f}x")


if __name__ == "__main__":
    try:
        # 测试哈希集成
        results = test_hash_integration()
        
        # 测试距离计算
        test_hamming_vs_cosine()
        
        print("\n=== 测试完成 ===")
        print("✅ 哈希功能集成成功！")
        print("\n使用方法:")
        print("1. 在配置文件中设置 'learnable_hash.enabled': true")
        print("2. 正常运行 train_new.py 和 test_new.py")
        print("3. 系统会自动使用哈希特征和汉明距离")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
