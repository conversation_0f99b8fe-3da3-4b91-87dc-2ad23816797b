import argparse
import os
import torch
from tqdm import tqdm
import numpy as np
from tabulate import tabulate

from lib.utils import logger
from lib.utils.config import Config
from lib.datasets.dataloader_utils import init_dataloader
# 不再需要导入DINO相关模块
from lib.datasets.linemod.dataloader_dino import LINEMODQuery, TemplatesLINEMOD
from lib.datasets.im_transform import tensor2im
from lib.datasets.linemod import inout
from lib.datasets.linemod import testing_utils_dino as testing_utils


# 简单CNN特征包装器
class DINO9RawWrapper(torch.nn.Module):
    def __init__(self, cnn_extractor):
        super().__init__()
        self.cnn_extractor = cnn_extractor
        self.feature_dim = cnn_extractor.feature_dim

    def forward(self, x):
        # 使用CNN提取特征
        cls_tokens_list, patch_tokens_list = self.cnn_extractor(x)
        # cls_tokens_list: [B, C, 1]，patch_tokens_list: [B, C, H, W]
        cls_token = cls_tokens_list[0].squeeze(-1)  # [B, C]
        patch_map = patch_tokens_list[0]  # [B, C, H, W]
        return {'cls_feature': cls_token, 'pose_feature': patch_map}

    @property
    def use_cad_feature(self):
        return False

    def eval(self):
        self.cnn_extractor.eval()
        return super().eval()


# 简单的CNN特征提取器
class SimpleCNNExtractor(torch.nn.Module):
    def __init__(self, device="cuda", feature_blocks_config=None, version="dinov2_vitb14", input_size=448):
        super().__init__()
        print("Creating Simple CNN feature extractor...")

        # 根据version确定特征维度
        self.model_dims = {
            "dinov2_vits14_reg": 384,
            "dinov2_vitb14_reg": 768,
            "dinov2_vitl14_reg": 1024,
            "dinov2_vitg14_reg": 1536
        }
        if version not in self.model_dims:
            raise ValueError(f"Unsupported version: {version}. Must be one of {list(self.model_dims.keys())}")

        self.feature_dim = self.model_dims[version]
        self.input_size = input_size
        self.device = device

        # 简单的CNN架构（已简化为两层）
        self.features = torch.nn.Sequential(
            # 输入: [B, 3, H, W]
            torch.nn.Conv2d(3, 64, kernel_size=7, stride=2, padding=3),  # 64
            torch.nn.ReLU(inplace=True),
            torch.nn.MaxPool2d(kernel_size=3, stride=2, padding=1),
            torch.nn.Conv2d(64, 128, kernel_size=3, stride=2, padding=1),  # 128
            torch.nn.ReLU(inplace=True),
            torch.nn.MaxPool2d(kernel_size=3, stride=2, padding=1),
            torch.nn.Conv2d(128, self.feature_dim, kernel_size=3, stride=2, padding=1),  # feature_dim
            torch.nn.ReLU(inplace=True),
        )

        # 全局平均池化用于CLS token
        self.global_pool = torch.nn.AdaptiveAvgPool2d(1)

        self.to(device)
        self.eval()

        # 计算输出特征图尺寸
        with torch.no_grad():
            dummy_input = torch.randn(1, 3, input_size, input_size).to(device)
            dummy_output = self.features(dummy_input)
            self.spatial_size = dummy_output.shape[2]

        print(f"Simple CNN with feature dimension {self.feature_dim}")
        print(f"Input size: {input_size}×{input_size} → Spatial features: {self.spatial_size}×{self.spatial_size}")
        print("Simple CNN feature extractor loaded.")

    def forward(self, x):
        # 调整输入尺寸
        x = torch.nn.functional.interpolate(x, size=(self.input_size, self.input_size), mode='bilinear',
                                            align_corners=False)

        # 提取特征图
        feature_map = self.features(x)  # [B, feature_dim, H, W]

        # 生成CLS token (全局平均池化)
        cls_token = self.global_pool(feature_map).squeeze(-1).squeeze(-1)  # [B, feature_dim]

        # 为了保持接口一致，返回list格式
        cls_tokens_list = [cls_token.unsqueeze(-1)]  # [B, feature_dim, 1]
        patch_tokens_list = [feature_map]  # [B, feature_dim, H, W]

        return cls_tokens_list, patch_tokens_list


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument('--split', type=str, choices=['split1', 'split2', 'split3'])
    parser.add_argument('--config_path', type=str, default='config_run/LM_DINO_split1.json')
    parser.add_argument('--checkpoint', type=str, default=None)
    parser.add_argument('--result_path', type=str, default='./dataset/results/vis/linemod')
    args = parser.parse_args()

    config_global = Config(config_file="./config.json").get_config()
    config_run = Config(args.config_path).get_config()

    args.result_path = os.path.join(args.result_path, config_run.dataset.split)
    if not os.path.exists(args.result_path):
        os.makedirs(args.result_path)

    dir_name = (args.config_path.split('/')[-1]).split('.')[0]
    save_path = os.path.join(config_global.root_path, config_run.log.weights, dir_name)
    trainer_dir = os.getcwd()
    trainer_logger = logger.init_logger(save_path=save_path,
                                        trainer_dir=trainer_dir,
                                        trainer_logger_name=dir_name)

    # 初始化简单的CNN特征提取器
    cnn_extractor = SimpleCNNExtractor(
        device="cuda" if torch.cuda.is_available() else "cpu",
        feature_blocks_config={'indices': [9]},
        version=config_run.model.version,
        input_size=config_run.model.input_size
    )
    model = DINO9RawWrapper(cnn_extractor).cuda()
    model.eval()

    # dataloader部分与test_new.py一致
    seen_id_obj, seen_names, seen_occ_id_obj, seen_occ_names, unseen_id_obj, unseen_names, \
        unseen_occ_id_obj, unseen_occ_names = inout.get_list_id_obj_from_split_name(config_run.dataset.split)

    config_loader = [
        ["seen_test", "seen_test", "LINEMOD", seen_id_obj],
        ["unseen_test", "test", "LINEMOD", unseen_id_obj],
        ["seen_template", "test", "templatesLINEMOD", seen_id_obj],
        ["unseen_template", "test", "templatesLINEMOD", unseen_id_obj],
        ["seen_occ_test", "test", "occlusionLINEMOD", seen_occ_id_obj],
        ["unseen_occ_test", "test", "occlusionLINEMOD", unseen_occ_id_obj],
        ["seen_occ_template", "test", "templatesLINEMOD", seen_occ_id_obj],
        ["unseen_occ_template", "test", "templatesLINEMOD", unseen_occ_id_obj]
    ]

    use_cad_feature = config_run.model.cad_feature.get('enabled', False)
    datasetLoader = {}
    for config in config_loader:
        print("Dataset", config[0], config[2], config[3])
        if config[2] == "templatesLINEMOD":
            save_sample_path = os.path.join(config_global.root_path, config_run.dataset.sample_path, dir_name,
                                            config[0])
            loader = TemplatesLINEMOD(
                root_dir=config_global.root_path,
                dataset=config[2],
                list_id_obj=config[3],
                split=config[1],
                image_size=config_run.dataset.image_size,
                save_path=save_sample_path,
                use_normalize=config_run.dataset.get('use_normalize', True),
                use_cad_feature=use_cad_feature
            )
        else:
            save_sample_path = os.path.join(config_global.root_path, config_run.dataset.sample_path, dir_name,
                                            config[0])
            loader = LINEMODQuery(
                root_dir=config_global.root_path,
                dataset=config[2],
                list_id_obj=config[3],
                split=config[1],
                image_size=config_run.dataset.image_size,
                save_path=save_sample_path,
                use_normalize=config_run.dataset.get('use_normalize', True),
                use_mask=config_run.dataset.get('use_mask', False),
                gt_bbox_known=config_run.dataset.get('gt_bbox_known', True),
                use_aug=config_run.dataset.get('use_aug', False),
                aug_config=config_run.dataset.get('aug', None),
                num_negatives=config_run.dataset.get('num_negatives', 1),
                use_cad_feature=use_cad_feature
            )
        datasetLoader[config[0]] = loader
        print("---" * 20)

    datasetLoader = init_dataloader(dict_dataloader=datasetLoader,
                                    batch_size=config_run.train.batch_size,
                                    num_workers=config_run.train.num_workers)

    # 测试主流程，直接复用testing_utils_dino.test
    print("Starting testing...")
    new_score = {}
    with torch.no_grad():
        for config_split in [["seen", seen_id_obj], ["unseen", unseen_id_obj], ["seen_occ", seen_occ_id_obj],
                             ["unseen_occ", unseen_occ_id_obj]]:
            query_name = config_split[0] + "_test"
            template_name = config_split[0] + "_template"
            print(f"\nTesting {config_split[0]}...")
            testing_score = testing_utils.test(
                query_data=datasetLoader[query_name],
                template_data=datasetLoader[template_name],
                model=model,
                split_name=config_split[0],
                list_id_obj=config_split[1].tolist(),
                epoch=-1,
                logger=trainer_logger,
                vis=False,
                result_vis_path=args.result_path,
                tensor2im=tensor2im,
                gt_bbox_known=config_run.dataset.gt_bbox_known
            )
            new_score.update(testing_score)
            print(f"\n{config_split[0].upper()} Results:")
            print("=" * 50)
            metrics = ["error", "accuracy", "recognition", "recognition and pose"]
            headers = ["Object"] + metrics
            table_data = []
            mean_row = ["Mean"]
            for metric in metrics:
                key = f"{metric}, mean"
                mean_row.append(f"{testing_score[key]:.4f}" if key in testing_score else "N/A")
            table_data.append(mean_row)
            for id_obj in config_split[1]:
                obj_name = inout.LINEMOD_real_id_to_name[id_obj]
                row = [obj_name]
                for metric in metrics:
                    key = f"{metric}, {obj_name}"
                    row.append(f"{testing_score[key]:.4f}" if key in testing_score else "N/A")
                table_data.append(row)
            print(tabulate(table_data, headers=headers, tablefmt="grid", floatfmt=".4f"))
            print("\n" + "=" * 50)

    print("\n" + "=" * 60)
    print("FINAL SUMMARY")
    print("=" * 60)
    all_splits = ["seen", "unseen", "seen_occ", "unseen_occ"]
    overall_metrics = {}
    for metric in ["error", "accuracy", "recognition", "recognition and pose"]:
        values = []
        for split in all_splits:
            key = f"{metric}, mean"
            if key in new_score:
                values.append(new_score[key])
        if values:
            overall_metrics[metric] = np.mean(values)
    print("Overall Average Results:")
    for metric, value in overall_metrics.items():
        print(f"{metric.capitalize()}: {value:.4f}")
    print(f"\nTesting completed successfully!")
    print(f"Results saved to: {args.result_path}") 