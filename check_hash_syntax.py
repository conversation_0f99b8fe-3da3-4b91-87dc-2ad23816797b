"""
检查哈希功能代码的语法正确性
"""

import ast
import sys

def check_syntax(file_path):
    """检查Python文件的语法"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            source = f.read()
        
        # 尝试解析AST
        ast.parse(source)
        print(f"✅ {file_path}: 语法正确")
        return True
    except SyntaxError as e:
        print(f"❌ {file_path}: 语法错误")
        print(f"   行 {e.lineno}: {e.text}")
        print(f"   错误: {e.msg}")
        return False
    except Exception as e:
        print(f"❌ {file_path}: 其他错误 - {e}")
        return False

def main():
    """检查所有相关文件"""
    files_to_check = [
        'lib/models/learnable_hash.py',
        'test_hash_functionality.py',
        'config_run/LM_DINO_split1.json'
    ]
    
    print("=== 检查哈希功能代码语法 ===\n")
    
    all_good = True
    
    for file_path in files_to_check:
        if file_path.endswith('.json'):
            # 检查JSON文件
            try:
                import json
                with open(file_path, 'r', encoding='utf-8') as f:
                    json.load(f)
                print(f"✅ {file_path}: JSON格式正确")
            except json.JSONDecodeError as e:
                print(f"❌ {file_path}: JSON格式错误 - {e}")
                all_good = False
            except Exception as e:
                print(f"❌ {file_path}: 文件错误 - {e}")
                all_good = False
        else:
            # 检查Python文件
            if not check_syntax(file_path):
                all_good = False
    
    print(f"\n=== 检查结果 ===")
    if all_good:
        print("🎉 所有文件语法正确！")
        print("\n下一步:")
        print("1. 在PyCharm中运行 test_hash_functionality.py")
        print("2. 或者直接在训练中启用哈希功能")
        print("3. 在配置文件中设置 'learnable_hash.enabled': true")
    else:
        print("⚠️  发现语法错误，请修复后再使用")

if __name__ == "__main__":
    main()
