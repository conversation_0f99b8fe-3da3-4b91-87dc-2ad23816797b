#!/usr/bin/env python
"""
调试真实DINO提取器输出格式
"""

import torch
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from lib.utils.config import Config
from lib.models.dino_feature_network import DINOv2FeatureExtractor

def debug_real_dino():
    """调试真实DINO提取器"""
    print("🔍 调试真实DINO提取器输出格式...")
    
    # 加载配置
    config_path = "config_run/LM_DINO_split1.json"
    config = Config(config_path)
    
    print(f"📊 配置层数: {config.model.feature_blocks.indices}")
    
    # 创建DINO提取器
    device = "cuda" if torch.cuda.is_available() else "cpu"
    dino_extractor = DINOv2FeatureExtractor(
        feature_blocks=config.model.feature_blocks,
        device=device
    )
    
    # 创建测试输入
    batch_size = 2
    test_input = torch.randn(batch_size, 3, 224, 224, device=device)
    
    print(f"📊 测试输入形状: {test_input.shape}")
    
    # 提取特征
    with torch.no_grad():
        cls_tokens_raw, patch_tokens_raw = dino_extractor(test_input)
    
    print(f"\n🔍 DINO提取器输出格式:")
    print(f"   CLS tokens type: {type(cls_tokens_raw)}")
    print(f"   Patch tokens type: {type(patch_tokens_raw)}")
    
    if isinstance(cls_tokens_raw, list):
        print(f"   CLS tokens count: {len(cls_tokens_raw)}")
        for i, cls_token in enumerate(cls_tokens_raw):
            print(f"   CLS Token {i}: {cls_token.shape}")
    else:
        print(f"   CLS tokens (single): {cls_tokens_raw.shape}")
    
    if isinstance(patch_tokens_raw, list):
        print(f"   Patch tokens count: {len(patch_tokens_raw)}")
        for i, patch_token in enumerate(patch_tokens_raw):
            print(f"   Patch Token {i}: {patch_token.shape}")
    else:
        print(f"   Patch tokens (single): {patch_tokens_raw.shape}")
    
    # 检查第一个CLS token的详细信息
    if isinstance(cls_tokens_raw, list) and len(cls_tokens_raw) > 0:
        first_cls = cls_tokens_raw[0]
        print(f"\n📊 第一个CLS token详细信息:")
        print(f"   形状: {first_cls.shape}")
        print(f"   维度: {first_cls.dim()}")
        print(f"   数据类型: {first_cls.dtype}")
        print(f"   设备: {first_cls.device}")
        
        # 测试squeeze操作
        if first_cls.dim() == 3:
            squeezed = first_cls.squeeze(-1)
            print(f"   Squeeze(-1)后: {squeezed.shape}")
        elif first_cls.dim() == 2:
            print(f"   已经是2D，无需squeeze")
        else:
            print(f"   意外的维度: {first_cls.dim()}")

if __name__ == "__main__":
    debug_real_dino() 