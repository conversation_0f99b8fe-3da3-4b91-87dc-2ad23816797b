import torch
import torch.nn as nn
import torch.nn.functional as F

from lib.losses.contrast_loss import InfoNCE, OcclusionAwareSimilarity
from lib.models.base_network import BaseFeatureExtractor
from lib.models.learnable_hash import HashAugmentedFeatureExtractor

# 添加SAM相关导入
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../EfficientSAM'))
from efficient_sam.build_efficient_sam import build_efficient_sam_vitt


class DINOResBlock(nn.Module):
    """
    为DINO特征设计的ResBlock：
    - 参考原始Diffusion版本的ResBlock设计
    - 适配DINO的高维特征到低维描述符的映射
    - 保持空间维度不变（无降采样）
    - 使用PyTorch默认的Kaiming Uniform初始化
    """
    def __init__(self, feature_dims, projection_dim, downsample_rate=2):
        super(DINOResBlock, self).__init__()
        
        # 主分支：3层卷积
        self.conv1 = nn.Sequential(
            nn.Conv2d(feature_dims, feature_dims // downsample_rate, kernel_size=1, bias=True),
            nn.ReLU(),
        )

        self.conv2 = nn.Sequential(
            nn.Conv2d(feature_dims // downsample_rate, projection_dim, kernel_size=3, padding=1, stride=1, bias=True),
            nn.ReLU(),
        )

        self.conv3 = nn.Conv2d(projection_dim, projection_dim, kernel_size=1, bias=True)

        # 残差分支：直接映射到目标维度
        self.shortcut = nn.Conv2d(feature_dims, projection_dim, kernel_size=1, stride=1, padding=0, bias=True)

    def forward(self, x):
        # 主分支：conv1 -> conv2 -> conv3
        out = self.conv1(x)
        out = self.conv2(out)
        out = self.conv3(out)
        
        # 残差连接
        residual = self.shortcut(x)
        
        # 输出：主分支 + 残差分支
        return out + residual


class SpatialAwareSimplifiedDINOAggregation(nn.Module):
    """
    空间感知的简化DINO聚合器：
    - 保留空间信息（参考原始FusionModule）
    - 避免过度复杂的注意力机制
    - 专注于简单有效的特征融合
    - 使用特征图模式
    """
    def __init__(self, feature_dim, descriptor_size=16, device="cuda"):
        super().__init__()
        self.feature_dim = feature_dim
        self.descriptor_size = descriptor_size
        self.device = device
        
        # 参考原始FusionModule的空间感知权重学习
        num_layers = 4  # 对应[9,10,11,12]四层
        self.num_layers = num_layers
        
        # 空间感知的权重生成器（类似FusionModule）
        self.spatial_weighting = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),  # 空间维度的全局池化
            nn.Flatten(),
            nn.Linear(feature_dim * num_layers, num_layers, bias=True),
        ).to(device)
        
        # 空间特征投影器：使用ResBlock结构（更强的特征提取能力）
        self.spatial_projection = DINOResBlock(
            feature_dims=feature_dim,
            projection_dim=descriptor_size,
            downsample_rate=2
        ).to(device)
        
        # CLS特征投影器
        self.cls_projection = nn.Sequential(
            nn.Linear(feature_dim, descriptor_size),
            nn.ReLU(),
            nn.Linear(descriptor_size, descriptor_size)
        ).to(device)
        
        # 初始化权重（类似原始FusionModule）
        self._init_weights()
        
        print(f"✅ Spatial-Aware Simplified DINO Aggregation initialized:")
        print(f"   📊 Feature dim: {feature_dim}")
        print(f"   📊 Descriptor size: {descriptor_size}")
        print(f"   📊 Number of layers: {num_layers}")
        print(f"   🌍 Spatial information: PRESERVED")
        print(f"   🏗️ Spatial projection: DINOResBlock (enhanced from simple Conv)")
        print(f"   🔄 Pose feature format: Spatial Maps [B,D,H,W]")
        
    def _init_weights(self):
        """参考原始FusionModule的初始化"""
        for m in self.spatial_weighting:
            if isinstance(m, nn.Linear):
                m.weight.data.fill_(0.)
                m.bias.data.fill_(1.)
                
    def forward(self, cls_tokens, patch_tokens, mask=None):
        """
        空间感知的前向传播
        Args:
            cls_tokens: list of [B, feature_dim, 1] from different layers
            patch_tokens: list of [B, feature_dim, 256] from different layers
            mask: [B, H, W] mask tensor，可选
        Returns:
            dict: {'cls_feature': cls_feature, 'pose_feature': pose_feature}
        """
        batch_size = cls_tokens[0].size(0)
        
        # 1. 处理CLS特征 - 简单加权融合（无空间维度）
        cls_features = []
        for cls_token in cls_tokens:
            cls_squeezed = cls_token.squeeze(-1)  # [B, feature_dim]
            cls_features.append(cls_squeezed)
        
        # 简单的层权重学习
        cls_stack = torch.stack(cls_features, dim=0)  # [num_layers, B, feature_dim]
        layer_weights = F.softmax(torch.ones(self.num_layers, device=self.device), dim=0)
        cls_fused = torch.sum(cls_stack * layer_weights.view(-1, 1, 1), dim=0)  # [B, feature_dim]
        cls_feature = self.cls_projection(cls_fused)
        cls_feature = F.normalize(cls_feature, p=2, dim=-1)
        
        # 2. 处理Patch特征 - 保留空间信息（关键改进！）
        # 将patch tokens重塑为空间特征图
        spatial_features = []
        for patch_token in patch_tokens:
            # [B, feature_dim, L] -> [B, feature_dim, H, W] 其中L=H*W
            B, C, L = patch_token.shape
            H = W = int(L ** 0.5)  # 动态计算空间尺寸，如：448/14=32 -> H=W=32
            spatial_feat = patch_token.view(B, C, H, W)  # [B, feature_dim, H, W]
            spatial_features.append(spatial_feat)
        
        # 拼接所有层的空间特征
        concat_spatial = torch.cat(spatial_features, dim=1)  # [B, feature_dim*num_layers, H, W]
        
        # 使用空间感知权重（参考FusionModule的设计）
        spatial_weights = F.softmax(self.spatial_weighting(concat_spatial), dim=1)  # [B, num_layers]
        
        # 空间级别的加权融合（保留空间信息！）
        fused_spatial = None
        for idx in range(self.num_layers):
            start, end = idx * self.feature_dim, (idx + 1) * self.feature_dim
            layer_spatial = concat_spatial[:, start:end, :, :]  # [B, feature_dim, H, W]
            
            # 逐像素加权（类似原始FusionModule的torch.einsum）
            weight = spatial_weights[:, idx].view(-1, 1, 1, 1)  # [B, 1, 1, 1]
            weighted_spatial = weight * layer_spatial  # [B, feature_dim, H, W]
            
            if fused_spatial is None:
                fused_spatial = weighted_spatial
            else:
                fused_spatial = fused_spatial + weighted_spatial
        
        # 投影到目标维度，但保持空间结构
        pose_spatial = self.spatial_projection(fused_spatial)  # [B, descriptor_size, H, W]
        
        # 特征图模式：对空间特征图进行逐像素归一化（保持空间结构）
        pose_feature = F.normalize(pose_spatial, p=2, dim=1)  # [B, descriptor_size, H, W]

        # 计算池化版本（用于对比和可视化）
        if mask is not None:
            # 使用mask引导的空间池化
            pose_pooled = self._mask_guided_spatial_pooling(pose_spatial, mask)
        else:
            # 全局平均池化
            pose_pooled = pose_spatial.mean(dim=[2, 3])  # [B, descriptor_size]

        pose_pooled = F.normalize(pose_pooled, p=2, dim=-1)

        return {
            'cls_feature': cls_feature,  # [B, descriptor_size] - 池化后的分类特征
            'pose_feature': pose_feature,  # [B, D, H, W] - 特征图
            'pose_pooled': pose_pooled,  # [B, descriptor_size] - 池化版本
            'spatial_features': pose_spatial  # [B, descriptor_size, H, W] 原始空间特征图
        }
    
    def _mask_guided_spatial_pooling(self, spatial_features, mask):
        """
        使用mask引导的空间池化（保留空间感知能力）
        Args:
            spatial_features: [B, descriptor_size, H, W] 空间特征图
            mask: [B, H_img, W_img] 输入mask
        Returns:
            pooled_features: [B, descriptor_size]
        """
        B, C, H, W = spatial_features.shape
        
        # 将mask缩放到特征图尺寸
        mask_resized = F.interpolate(
            mask.unsqueeze(1).float(), 
            size=(H, W), 
            mode='bilinear', 
            align_corners=False
        ).squeeze(1)  # [B, H, W]
        
        # 空间加权平均池化
        mask_weights = mask_resized.unsqueeze(1)  # [B, 1, H, W]
        weighted_sum = torch.sum(spatial_features * mask_weights, dim=[2, 3])  # [B, descriptor_size]
        mask_sum = torch.sum(mask_weights, dim=[2, 3]) + 1e-6  # [B, 1]
        
        pooled_features = weighted_sum / mask_sum  # [B, descriptor_size]
        
        return pooled_features
class SpatialAwareSimplifiedDINOFeatureExtractor(nn.Module):
    """
    空间感知的简化DINO特征提取器：
    - 使用SpatialAwareSimplifiedDINOAggregation
    - 支持空间信息保留或池化
    - 专注于简单有效的设计
    """
    def __init__(self, config, dino_extractor):
        super().__init__()
        self.dino_extractor = dino_extractor
        self.config = config

        # 获取特征维度和输出维度
        feature_dim = dino_extractor.feature_dim
        descriptor_size = config.model.descriptor_size

        print(f"🔧 Creating Spatial-Aware Simplified DINO Feature Extractor:")
        print(f"   📊 DINO feature dimension: {feature_dim}")
        print(f"   📊 Output descriptor size: {descriptor_size}")
        print(f"   🌍 Spatial information: PRESERVED")

        # 使用空间感知的聚合网络
        self.aggregation_network = SpatialAwareSimplifiedDINOAggregation(
            feature_dim=feature_dim,
            descriptor_size=descriptor_size,
            device="cuda"
        )
        
    def forward(self, x, cad_feature=None, mask=None):
        """
        前向传播
        Args:
            x: 输入图像张量 [B, 3, H, W]
            cad_feature: CAD特征，在此简化版本中被忽略
            mask: [B, H, W] mask tensor，可选
        Returns:
            dict: {'cls_feature': cls_feature, 'pose_feature': pose_feature, 'spatial_features': spatial_features}
        """
        # 使用图像进行在线特征提取
        cls_tokens_raw, patch_tokens_raw = self.dino_extractor(x)

        # 转换格式以匹配期望的输入
        if isinstance(cls_tokens_raw, list) and len(cls_tokens_raw) > 0:
            if cls_tokens_raw[0].dim() == 2:  # [4, 768]
                # 重新组织：从B个[4, 768] -> 4个[B, 768]
                cls_tokens = []
                patch_tokens = []
                num_layers = cls_tokens_raw[0].size(0)  # 4
                for layer_idx in range(num_layers):
                    cls_layer = torch.stack([cls_tokens_raw[b][layer_idx] for b in range(len(cls_tokens_raw))], dim=0)
                    patch_layer = torch.stack([patch_tokens_raw[b][layer_idx] for b in range(len(patch_tokens_raw))], dim=0)
                    cls_layer = cls_layer.unsqueeze(-1)  # [B, 768, 1]
                    cls_tokens.append(cls_layer)
                    patch_tokens.append(patch_layer)
            else:
                cls_tokens = cls_tokens_raw
                patch_tokens = patch_tokens_raw
        else:
            cls_tokens = cls_tokens_raw
            patch_tokens = patch_tokens_raw
            
        # 使用空间感知的聚合网络
        features = self.aggregation_network(cls_tokens, patch_tokens, mask)
        
        return features

    def calculate_similarity(self, query_features, template_features, feature_type='pose', mask=None):
        """
        计算特征相似度 - 自动适配池化版本和特征图版本
        Args:
            query_features: [B, D] 或 [B, D, H, W] 特征
            template_features: [B, D] 或 [B, D, H, W] 特征
            feature_type: 'pose' 或 'cls'
            mask: [B, H, W] mask tensor，可选，用于mask引导的相似度计算
        Returns:
            similarity: [B, B] 相似度矩阵
        """
        # 检查输入维度，判断是向量特征还是特征图
        if query_features.dim() == 2:  # [B, D] - 向量特征（cls或池化pose）
            return F.cosine_similarity(
                query_features.unsqueeze(1),  # [B, 1, D]
                template_features.unsqueeze(0),  # [1, B, D]
                dim=2
            )  # [B, B]
        
        elif query_features.dim() == 4:  # [B, D, H, W] - 特征图（空间pose）
            B, D, H, W = query_features.shape
            
            # 重塑为 [B, D, H*W]
            query_flat = query_features.view(B, D, -1)  # [B, D, H*W]
            template_flat = template_features.view(B, D, -1)  # [B, D, H*W]
            
            # 计算每个空间位置的相似度
            # query_flat: [B, D, H*W] -> [B, 1, D, H*W]
            # template_flat: [B, D, H*W] -> [1, B, D, H*W]
            query_expanded = query_flat.unsqueeze(1)  # [B, 1, D, H*W]
            template_expanded = template_flat.unsqueeze(0)  # [1, B, D, H*W]
            
            # 计算空间相似度：在特征维度上计算余弦相似度
            spatial_similarities = F.cosine_similarity(
                query_expanded,  # [B, 1, D, H*W]
                template_expanded,  # [1, B, D, H*W]
                dim=2  # 在D维度上计算相似度
            )  # [B, B, H*W]
            
            # 对空间维度取平均，得到最终相似度
            similarity = spatial_similarities.mean(dim=2)  # [B, B]
            
            return similarity
            
        else:
            raise ValueError(f"Unsupported feature dimension: {query_features.dim()}. Expected 2D or 4D tensors.")


# ====== 偏移量预测器 ======
class OffsetPredictorNetwork(nn.Module):
    """
    偏移量预测网络：输入query和template特征图，输出pose offset
    """
    def __init__(self, descriptor_size=128, pose_dim=3):
        super().__init__()

        # 卷积特征融合层 - 处理拼接后的特征图
        self.conv_fusion = nn.Sequential(
            nn.Conv2d(descriptor_size * 2, 256, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.Conv2d(256, 128, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.Conv2d(128, 64, kernel_size=3, padding=1),
            nn.ReLU()
        )

        # 全局平均池化
        self.global_pool = nn.AdaptiveAvgPool2d(1)

        # 偏移量预测头
        self.offset_head = nn.Sequential(
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(32, pose_dim)  # 输出3D偏移量 [dx, dy, dz]
        )

        # 初始化权重
        self._init_weights()

        print(f"✅ Offset Predictor Network initialized:")
        print(f"   📊 Input feature dim: {descriptor_size * 2} (feature maps)")
        print(f"   📊 Output pose dim: {pose_dim}")
        print(f"   🎯 Architecture: Conv Fusion + Global Pool + Offset Head")

    def _init_weights(self):
        for m in self.modules():
            if isinstance(m, (nn.Conv2d, nn.Linear)):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)

    def forward(self, query_features, template_features):
        """
        Args:
            query_features: [B, descriptor_size, H, W] query的pose特征图
            template_features: [B, descriptor_size, H, W] template的pose特征图
        Returns:
            offset: [B, 3] 预测的偏移量
        """
        # 拼接特征图
        combined_features = torch.cat([query_features, template_features], dim=1)  # [B, descriptor_size*2, H, W]

        # 卷积特征融合
        fused_features = self.conv_fusion(combined_features)  # [B, 64, H, W]

        # 全局平均池化
        pooled_features = self.global_pool(fused_features)  # [B, 64, 1, 1]
        pooled_features = pooled_features.view(pooled_features.size(0), -1)  # [B, 64]

        # 预测偏移量
        offset = self.offset_head(pooled_features)  # [B, 3]

        return offset


class OffsetPredictor:
    """
    偏移量预测器工具类
    用于训练和测试中的串联使用，不破坏特征提取器的接口一致性
    """
    def __init__(self, config):
        """
        Args:
            config: 配置对象，包含offset_predictor配置
        """
        self.config = config
        self.descriptor_size = config.model.descriptor_size  # 直接使用配置中的值
        self.pose_dim = getattr(config.model.offset_predictor, 'pose_dim', 3)

        # 立即创建偏移量预测网络
        self.offset_predictor = OffsetPredictorNetwork(
            descriptor_size=self.descriptor_size,
            pose_dim=self.pose_dim
        ).cuda()

        print(f"🔧 Offset Predictor initialized:")
        print(f"   📊 Descriptor size: {self.descriptor_size}")
        print(f"   📊 Pose dimension: {self.pose_dim}")
        print(f"   ✅ Ready for training and testing")

    def predict_offset(self, query_features, template_features):
        """
        预测偏移量
        Args:
            query_features: dict, 包含'pose_feature'键的特征字典
            template_features: dict, 包含'pose_feature'键的特征字典
        Returns:
            pose_offset: [B, pose_dim] 预测的偏移量
        """
        # 使用原始特征（hash之前的特征）进行offset预测
        query_pose_feature = query_features.get('original_pose_feature', query_features['pose_feature'])
        template_pose_feature = template_features.get('original_pose_feature', template_features['pose_feature'])

        return self.offset_predictor(
            query_pose_feature,    # [B, D, H, W] 原始特征
            template_pose_feature  # [B, D, H, W] 原始特征
        )

    def load_state_dict(self, state_dict):
        """加载预训练权重"""
        # 从完整的state_dict中提取offset_predictor相关的权重
        offset_state_dict = {}
        for key, value in state_dict.items():
            if key.startswith('offset_predictor.'):
                new_key = key.replace('offset_predictor.', '')
                offset_state_dict[new_key] = value

        if offset_state_dict:
            self.offset_predictor.load_state_dict(offset_state_dict)
            print("✅ Loaded offset predictor weights from checkpoint")
        else:
            print("⚠️  No offset predictor weights found in checkpoint")


def _create_base_feature_extractor(config, dino_extractor):
    """
    内部函数：创建基础特征提取器（不包含偏移量预测器）
    """
    descriptor_size = config.model.descriptor_size

    # 检查模态启用状态
    enable_sam = getattr(config.model.efficient_sam, 'enabled', False)
    enable_dino = getattr(config.model, 'backbone', 'dinov2') == 'dinov2'

    # 检查是否启用Cross-Modal模式
    enable_cross_modal = getattr(config.model.efficient_sam, 'cross_modal', False)

    # 简化的模式选择逻辑
    if enable_sam and enable_dino:
        if enable_cross_modal:
            # Cross-Modal DINO+SAM模式
            print("🔍 Using Cross-Modal DINO+SAM Feature Extractor!")
            print(f"   📊 DINO feature dim: 1024, SAM feature dim: 192")
            print(f"   📊 Combined feature dim: Advanced Cross-Modal Fusion → {descriptor_size}")
            print(f"   🌍 Spatial information: PRESERVED")
            print(f"   🔄 Cross-Modal Attention: ENABLED")
            print(f"   🎯 Multi-Scale Fusion: ENABLED")
            print(f"   🚪 Adaptive Gating: ENABLED")
            return CrossModalDINO_SAM_FeatureExtractor(config, dino_extractor)
        else:
            # 传统DINO+SAM双模态模式
            print("🔍 Using Spatial-Aware DINO+SAM Feature Extractor!")
            print(f"   📊 DINO feature dim: 1024, SAM feature dim: 192")
            print(f"   📊 Combined feature dim: 1216 → {descriptor_size}")
            print(f"   🌍 Spatial information: PRESERVED")
            return SpatialAwareDINO_SAM_FeatureExtractor(config, dino_extractor)
    elif enable_sam and not enable_dino:
        # 纯SAM模式
        print("🔍 Using Spatial-Aware SAM-Only Feature Extractor!")
        print(f"   📊 SAM feature dim: 192 → {descriptor_size}")
        print(f"   🌍 Spatial information: PRESERVED")
        print(f"   ⚡ Model complexity: SAM-ONLY")
        print(f"   🔄 Feature mode: Spatial Maps")
        return SpatialAwareSAMOnlyFeatureExtractor(config)
    elif not enable_sam and enable_dino:
        # 纯DINO模式
        print("🔍 Using Spatial-Aware Simplified DINO Feature Extractor:")
        print(f"   📊 DINO feature dim: 1024 → {descriptor_size}")
        print(f"   🌍 Spatial information: PRESERVED")
        print(f"   ⚡ Model complexity: SIMPLIFIED")
        print(f"   🔄 Feature mode: Spatial Maps")
        return SpatialAwareSimplifiedDINOFeatureExtractor(config, dino_extractor)
    else:
        # 都没启用，默认使用DINO
        print("⚠️  警告: 没有启用任何特征提取器，默认使用DINO模式")
        print("🔍 Using Spatial-Aware Simplified DINO Feature Extractor (Default):")
        print(f"   📊 DINO feature dim: 1024 → {descriptor_size}")
        print(f"   🌍 Spatial information: PRESERVED")
        return SpatialAwareSimplifiedDINOFeatureExtractor(config, dino_extractor)
def create_feature_extractor(config, dino_extractor):
    """
    工厂函数：根据配置智能选择特征提取器，并可选地包装哈希功能

    Args:
        config: 配置对象
        dino_extractor: DINO特征提取器 (SAM-Only模式时可以为None)

    Returns:
        HashAugmentedFeatureExtractor 或 基础特征提取器
    """
    # 创建基础特征提取器
    base_extractor = _create_base_feature_extractor(config, dino_extractor)

    # Hash功能现在独立处理，不再包装到模型中
    return base_extractor


# ====== SAM+DINO空间感知聚合器 ======
class SpatialAwareDINO_SAM_Aggregation(nn.Module):
    """
    DINO+SAM空间感知聚合器：
    - 拼接DINO和SAM多层特征后再聚合
    - 保持与原有聚合器相同的接口
    - 使用特征图模式保留空间信息
    """
    def __init__(self, feature_dim, sam_dim, descriptor_size=16, device="cuda"):
        super().__init__()
        self.feature_dim = feature_dim
        self.sam_dim = sam_dim
        self.descriptor_size = descriptor_size
        self.device = device
        
        # 拼接后总特征维度
        self.concat_dim = feature_dim + sam_dim
        
        # 固定4层（对应DINO的[9,10,11,12]层）
        num_layers = 4
        self.num_layers = num_layers
        
        # 空间感知的权重生成器
        self.spatial_weighting = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Flatten(),
            nn.Linear(self.concat_dim * num_layers, num_layers, bias=True),
        ).to(device)
        
        # 空间特征投影器：使用ResBlock结构 (融合分支)
        self.spatial_projection = DINOResBlock(
            feature_dims=self.concat_dim,
            projection_dim=descriptor_size,
            downsample_rate=2
        ).to(device)

        # 残差分支：纯DINO特征投影器
        self.dino_residual_projection = DINOResBlock(
            feature_dims=feature_dim,
            projection_dim=descriptor_size,
            downsample_rate=2
        ).to(device)

        # CLS特征投影器 (融合分支)
        self.cls_projection = nn.Sequential(
            nn.Linear(self.concat_dim, descriptor_size),
            nn.ReLU(),
            nn.Linear(descriptor_size, descriptor_size)
        ).to(device)

        # 残差分支：纯DINO CLS投影器
        self.dino_cls_residual_projection = nn.Sequential(
            nn.Linear(feature_dim, descriptor_size),
            nn.ReLU(),
            nn.Linear(descriptor_size, descriptor_size)
        ).to(device)
        
        # 初始化权重
        self._init_weights()
        
        print(f"✅ Spatial-Aware DINO+SAM Aggregation initialized (with Residual Connection):")
        print(f"   📊 DINO feature dim: {feature_dim}")
        print(f"   📊 SAM feature dim: {sam_dim}")
        print(f"   📊 Combined dim: {self.concat_dim}")
        print(f"   📊 Descriptor size: {descriptor_size}")
        print(f"   🌍 Spatial information: PRESERVED")
        print(f"   🔗 Residual connection: DINO features as residual branch")
        
    def _init_weights(self):
        """初始化权重"""
        for m in self.spatial_weighting:
            if isinstance(m, nn.Linear):
                m.weight.data.fill_(0.)
                m.bias.data.fill_(1.)
                
    def forward(self, cls_tokens, patch_tokens, sam_cls_tokens, sam_patch_tokens, mask=None):
        """
        前向传播：融合DINO和SAM特征
        Args:
            cls_tokens: list of [B, feature_dim, 1] DINO cls tokens
            patch_tokens: list of [B, feature_dim, 256] DINO patch tokens
            sam_cls_tokens: list of [B, sam_dim, 1] SAM cls tokens
            sam_patch_tokens: list of [B, sam_dim, 256] SAM patch tokens
            mask: [B, H, W] mask tensor，可选
        Returns:
            dict: {'cls_feature': cls_feature, 'pose_feature': pose_feature, 'pose_pooled': pose_pooled}
        """
        # 1. 处理CLS特征：融合分支 + 残差分支
        # 融合分支：拼接DINO和SAM的cls tokens
        cls_features = []
        dino_cls_features = []
        for dino_cls, sam_cls in zip(cls_tokens, sam_cls_tokens):
            dino_cls = dino_cls.squeeze(-1)  # [B, feature_dim]
            sam_cls = sam_cls.squeeze(-1)    # [B, sam_dim]
            concat_cls = torch.cat([dino_cls, sam_cls], dim=1)  # [B, concat_dim]
            cls_features.append(concat_cls)
            dino_cls_features.append(dino_cls)  # 保存纯DINO特征用于残差

        # 融合分支：层权重学习
        cls_stack = torch.stack(cls_features, dim=0)  # [num_layers, B, concat_dim]
        layer_weights = F.softmax(torch.ones(self.num_layers, device=self.device), dim=0)
        cls_fused = torch.sum(cls_stack * layer_weights.view(-1, 1, 1), dim=0)  # [B, concat_dim]
        cls_fusion = self.cls_projection(cls_fused)  # [B, descriptor_size]

        # 残差分支：纯DINO特征
        dino_cls_stack = torch.stack(dino_cls_features, dim=0)  # [num_layers, B, feature_dim]
        dino_cls_fused = torch.sum(dino_cls_stack * layer_weights.view(-1, 1, 1), dim=0)  # [B, feature_dim]
        cls_residual = self.dino_cls_residual_projection(dino_cls_fused)  # [B, descriptor_size]

        # 残差连接：融合分支 + 残差分支
        cls_feature = cls_fusion + cls_residual  # [B, descriptor_size]
        cls_feature = F.normalize(cls_feature, p=2, dim=-1)
        
        # 2. 处理空间特征：融合分支 + 残差分支
        # 融合分支：拼接DINO和SAM的patch tokens
        spatial_features = []
        dino_spatial_features = []
        for dino_patch, sam_patch in zip(patch_tokens, sam_patch_tokens):
            # DINO: [B, C, L] -> [B, C, H, W]
            B, C_dino, L_dino = dino_patch.shape
            H_dino = W_dino = int(L_dino ** 0.5)
            dino_map = dino_patch.view(B, C_dino, H_dino, W_dino)  # [B, feature_dim, H, W]

            # SAM: [B, C, L] -> [B, C, H, W] -> resize到DINO尺寸
            B, C_sam, L_sam = sam_patch.shape
            H_sam = W_sam = int(L_sam ** 0.5)
            sam_map = sam_patch.view(B, C_sam, H_sam, W_sam)  # [B, sam_dim, H_sam, W_sam]

            # 将SAM特征图resize到与DINO相同的空间尺寸
            if (H_sam, W_sam) != (H_dino, W_dino):
                sam_map = F.interpolate(
                    sam_map,
                    size=(H_dino, W_dino),
                    mode='bilinear',
                    align_corners=False
                )  # [B, sam_dim, H_dino, W_dino]

            concat_map = torch.cat([dino_map, sam_map], dim=1)  # [B, concat_dim, H, W]
            spatial_features.append(concat_map)
            dino_spatial_features.append(dino_map)  # 保存纯DINO特征用于残差
        
        # 融合分支：拼接所有层的空间特征
        concat_spatial = torch.cat(spatial_features, dim=1)  # [B, concat_dim*num_layers, H, W]

        # 使用空间感知权重
        spatial_weights = F.softmax(self.spatial_weighting(concat_spatial), dim=1)  # [B, num_layers]

        # 融合分支：空间级别的加权融合
        fused_spatial = None
        for idx in range(self.num_layers):
            start, end = idx * self.concat_dim, (idx + 1) * self.concat_dim
            layer_spatial = concat_spatial[:, start:end, :, :]  # [B, concat_dim, H, W]

            weight = spatial_weights[:, idx].view(-1, 1, 1, 1)  # [B, 1, 1, 1]
            weighted_spatial = weight * layer_spatial  # [B, concat_dim, H, W]

            if fused_spatial is None:
                fused_spatial = weighted_spatial
            else:
                fused_spatial = fused_spatial + weighted_spatial

        # 融合分支：投影到目标维度
        pose_fusion = self.spatial_projection(fused_spatial)  # [B, descriptor_size, H, W]

        # 残差分支：处理纯DINO空间特征
        dino_concat_spatial = torch.cat(dino_spatial_features, dim=1)  # [B, feature_dim*num_layers, H, W]
        dino_fused_spatial = None
        for idx in range(self.num_layers):
            start, end = idx * self.feature_dim, (idx + 1) * self.feature_dim
            dino_layer_spatial = dino_concat_spatial[:, start:end, :, :]  # [B, feature_dim, H, W]

            weight = spatial_weights[:, idx].view(-1, 1, 1, 1)  # [B, 1, 1, 1]
            dino_weighted_spatial = weight * dino_layer_spatial  # [B, feature_dim, H, W]

            if dino_fused_spatial is None:
                dino_fused_spatial = dino_weighted_spatial
            else:
                dino_fused_spatial = dino_fused_spatial + dino_weighted_spatial

        # 残差分支：投影到目标维度
        pose_residual = self.dino_residual_projection(dino_fused_spatial)  # [B, descriptor_size, H, W]

        # 残差连接：融合分支 + 残差分支
        pose_spatial = pose_fusion + pose_residual  # [B, descriptor_size, H, W]
        
        # 特征图模式：对空间特征图进行逐像素归一化
        pose_feature = F.normalize(pose_spatial, p=2, dim=1)  # [B, descriptor_size, H, W]

        # 计算池化版本（用于对比和可视化）
        if mask is not None:
            pose_pooled = self._mask_guided_spatial_pooling(pose_spatial, mask)
        else:
            pose_pooled = pose_spatial.mean(dim=[2, 3])  # [B, descriptor_size]

        pose_pooled = F.normalize(pose_pooled, p=2, dim=-1)

        return {
            'cls_feature': cls_feature,  # [B, descriptor_size]
            'pose_feature': pose_feature,  # [B, D, H, W] - 特征图
            'pose_pooled': pose_pooled,  # [B, descriptor_size]
            'spatial_features': pose_spatial  # [B, descriptor_size, H, W]
        }
    
    def _mask_guided_spatial_pooling(self, spatial_features, mask):
        """使用mask引导的空间池化"""
        B, C, H, W = spatial_features.shape
        
        # 将mask缩放到特征图尺寸
        mask_resized = F.interpolate(
            mask.unsqueeze(1).float(), 
            size=(H, W), 
            mode='bilinear', 
            align_corners=False
        ).squeeze(1)  # [B, H, W]
        
        # 空间加权平均池化
        mask_weights = mask_resized.unsqueeze(1)  # [B, 1, H, W]
        weighted_sum = torch.sum(spatial_features * mask_weights, dim=[2, 3])  # [B, descriptor_size]
        mask_sum = torch.sum(mask_weights, dim=[2, 3]) + 1e-6  # [B, 1]
        
        pooled_features = weighted_sum / mask_sum  # [B, descriptor_size]
        
        return pooled_features
class SpatialAwareDINO_SAM_FeatureExtractor(nn.Module):
    """
    DINO+SAM空间感知特征提取器：
    - 同时提取DINO和SAM特征
    - 使用SpatialAwareDINO_SAM_Aggregation进行特征融合
    - 保持与原有特征提取器相同的接口
    """
    def __init__(self, config, dino_extractor):
        super().__init__()
        self.dino_extractor = dino_extractor
        self.config = config
        
        # 初始化SAM编码器
        print("🔧 Initializing EfficientSAM encoder...")
        self.sam_encoder = build_efficient_sam_vitt()
        self.sam_encoder.eval()
        for p in self.sam_encoder.parameters():
            p.requires_grad = False
        
        # 获取特征维度
        feature_dim = dino_extractor.feature_dim
        descriptor_size = config.model.descriptor_size

        # 从配置中读取SAM特征维度，默认192
        sam_dim = getattr(config.model.efficient_sam, 'feature_dim', 192)

        # 获取DINO的层索引配置，用于SAM层对应 (配置中的层数需要转换为0基索引)
        config_indices = getattr(config.model.feature_blocks, 'indices', [9, 10, 11, 12])
        self.dino_layer_indices = [idx - 1 for idx in config_indices]  # 转换为0基索引
        
        print(f"🔧 Creating Spatial-Aware DINO+SAM Feature Extractor:")
        print(f"   📊 DINO feature dimension: {feature_dim}")
        print(f"   📊 SAM feature dimension: {sam_dim}")
        print(f"   📊 Output descriptor size: {descriptor_size}")
        print(f"   🌍 Spatial information: PRESERVED")

        # 使用DINO+SAM聚合网络
        self.aggregation_network = SpatialAwareDINO_SAM_Aggregation(
            feature_dim=feature_dim,
            sam_dim=sam_dim,
            descriptor_size=descriptor_size,
            device="cuda" if torch.cuda.is_available() else "cpu"
        )
        
    def extract_sam_features(self, x, target_layer_indices=None):
        """
        提取SAM特征，支持指定层数
        Args:
            x: 输入图像
            target_layer_indices: 目标层索引，如[9,10,11,12]，如果为None则提取所有层
        """
        if target_layer_indices is None:
            target_layer_indices = [8, 9, 10, 11]  # 默认与DINO对应的层 (0基索引)

        features = []

        def sam_hook_fn(module, input, output):
            # output: [B, H*W, C]
            features.append(output)  # [B, H*W, C] - 原始版本

        hooks = []
        # 只在目标层注册hook
        total_blocks = len(self.sam_encoder.image_encoder.blocks)
        for layer_idx in target_layer_indices:
            if layer_idx < total_blocks:
                hooks.append(
                    self.sam_encoder.image_encoder.blocks[layer_idx].register_forward_hook(sam_hook_fn)
                )
            else:
                print(f"警告: SAM层索引 {layer_idx} 超出范围 (总共{total_blocks}层)，使用最后一层")
                hooks.append(
                    self.sam_encoder.image_encoder.blocks[-1].register_forward_hook(sam_hook_fn)
                )

        with torch.no_grad():
            # 调整输入尺寸到SAM期望的分辨率
            x_resized = F.interpolate(x, size=(1024, 1024), mode='bilinear', align_corners=False)
            # SAM的前向传播
            _ = self.sam_encoder.image_encoder(x_resized)

        # 移除hooks
        for h in hooks:
            h.remove()

        # 处理特征
        sam_patch_tokens = features  # 直接使用捕获的特征
        sam_cls_tokens = [f.mean(dim=1, keepdim=True).transpose(1, 2) for f in sam_patch_tokens]  # [B, C, 1]

        # 转为[B, C, L]格式
        sam_patch_tokens = [f.transpose(1, 2) for f in sam_patch_tokens]  # [B, C, L]

        return sam_cls_tokens, sam_patch_tokens
        
    def forward(self, x, cad_feature=None, mask=None):
        """
        前向传播
        Args:
            x: 输入图像张量 [B, 3, H, W]
            cad_feature: CAD特征，在此版本中被忽略
            mask: [B, H, W] mask tensor，可选
        Returns:
            dict: {'cls_feature': cls_feature, 'pose_feature': pose_feature, 'spatial_features': spatial_features}
        """
        # 使用图像进行在线特征提取
        cls_tokens_raw, patch_tokens_raw = self.dino_extractor(x)

        # 转换DINO特征格式
        if isinstance(cls_tokens_raw, list) and len(cls_tokens_raw) > 0:
            if cls_tokens_raw[0].dim() == 2:  # [4, 768]
                # 重新组织：从B个[4, 768] -> 4个[B, 768]
                cls_tokens = []
                patch_tokens = []
                num_layers = cls_tokens_raw[0].size(0)  # 4
                for layer_idx in range(num_layers):
                    cls_layer = torch.stack([cls_tokens_raw[b][layer_idx] for b in range(len(cls_tokens_raw))], dim=0)
                    patch_layer = torch.stack([patch_tokens_raw[b][layer_idx] for b in range(len(patch_tokens_raw))], dim=0)
                    cls_layer = cls_layer.unsqueeze(-1)  # [B, 768, 1]
                    cls_tokens.append(cls_layer)
                    patch_tokens.append(patch_layer)
            else:
                cls_tokens = cls_tokens_raw
                patch_tokens = patch_tokens_raw
        else:
            cls_tokens = cls_tokens_raw
            patch_tokens = patch_tokens_raw

        # 提取SAM特征，使用与DINO相同的层索引
        sam_cls_tokens, sam_patch_tokens = self.extract_sam_features(x, self.dino_layer_indices)
        
        # SAM特征已经按照DINO的层索引提取，无需再次裁剪
        # 验证层数匹配
        assert len(cls_tokens) == len(sam_cls_tokens), f"DINO和SAM层数不匹配: {len(cls_tokens)} vs {len(sam_cls_tokens)}"
        
        # 使用DINO+SAM聚合网络
        features = self.aggregation_network(
            cls_tokens=cls_tokens,
            patch_tokens=patch_tokens,
            sam_cls_tokens=sam_cls_tokens,
            sam_patch_tokens=sam_patch_tokens,
            mask=mask
        )
        
        return features

    def calculate_similarity(self, query_features, template_features, feature_type='pose', mask=None):
        """
        计算特征相似度 - 复用原有逻辑
        Args:
            query_features: [B, D] 或 [B, D, H, W] 特征
            template_features: [B, D] 或 [B, D, H, W] 特征
            feature_type: 'pose' 或 'cls'
            mask: [B, H, W] mask tensor，可选，用于mask引导的相似度计算
        Returns:
            similarity: [B, B] 相似度矩阵
        """
        # 检查输入维度，判断是向量特征还是特征图
        if query_features.dim() == 2:  # [B, D] - 向量特征（cls或池化pose）
            return F.cosine_similarity(
                query_features.unsqueeze(1),  # [B, 1, D]
                template_features.unsqueeze(0),  # [1, B, D]
                dim=2
            )  # [B, B]
        
        elif query_features.dim() == 4:  # [B, D, H, W] - 特征图（空间pose）
            B, D, H, W = query_features.shape

            # 重塑为 [B, D, H*W]
            query_flat = query_features.view(B, D, -1)  # [B, D, H*W]
            template_flat = template_features.view(B, D, -1)  # [B, D, H*W]

            # 计算每个空间位置的相似度
            query_expanded = query_flat.unsqueeze(1)  # [B, 1, D, H*W]
            template_expanded = template_flat.unsqueeze(0)  # [1, B, D, H*W]

            # 计算空间相似度：在特征维度上计算余弦相似度
            spatial_similarities = F.cosine_similarity(
                query_expanded,  # [B, 1, D, H*W]
                template_expanded,  # [1, B, D, H*W]
                dim=2  # 在D维度上计算相似度
            )  # [B, B, H*W]

            # 如果提供了mask，使用mask引导的相似度计算
            if mask is not None:
                # 将mask缩放到特征图尺寸
                mask_resized = F.interpolate(
                    mask.unsqueeze(1).float(),  # [B, 1, H_img, W_img]
                    size=(H, W),
                    mode='bilinear',
                    align_corners=False
                ).squeeze(1)  # [B, H, W]

                # 重塑mask为 [B, H*W]
                mask_flat = mask_resized.view(B, -1)  # [B, H*W]

                # 扩展mask维度以匹配spatial_similarities: [B, B, H*W]
                mask_expanded = mask_flat.unsqueeze(1).expand(-1, B, -1)  # [B, B, H*W]

                # 应用mask权重到相似度计算
                masked_similarities = spatial_similarities * mask_expanded  # [B, B, H*W]

                # 计算加权平均相似度
                mask_sum = mask_expanded.sum(dim=2, keepdim=True) + 1e-6  # [B, B, 1]
                similarity = masked_similarities.sum(dim=2, keepdim=True) / mask_sum  # [B, B, 1]
                similarity = similarity.squeeze(-1)  # [B, B]
            else:
                # 对空间维度取平均，得到最终相似度
                similarity = spatial_similarities.mean(dim=2)  # [B, B]

            return similarity

        else:
            raise ValueError(f"Unsupported feature dimension: {query_features.dim()}. Expected 2D or 4D tensors.")


# ====== 纯SAM空间感知特征提取器 ======
class SpatialAwareSAMOnlyAggregation(nn.Module):
    """
    纯SAM空间感知聚合器：
    - 只使用SAM的第9层特征，不进行多层融合
    - 保持与其他聚合器相同的接口
    - 使用特征图模式保留空间信息
    """
    def __init__(self, sam_dim, descriptor_size=16, device="cuda"):
        super().__init__()
        self.sam_dim = sam_dim
        self.descriptor_size = descriptor_size
        self.device = device

        # 空间特征投影器：使用ResBlock结构
        self.spatial_projection = DINOResBlock(
            feature_dims=sam_dim,
            projection_dim=descriptor_size,
            downsample_rate=2
        ).to(device)

        # CLS特征投影器
        self.cls_projection = nn.Sequential(
            nn.Linear(sam_dim, descriptor_size),
            nn.ReLU(),
            nn.Linear(descriptor_size, descriptor_size)
        ).to(device)

        print(f"✅ Spatial-Aware SAM-Only Aggregation initialized (Single Layer):")
        print(f"   📊 SAM feature dim: {sam_dim}")
        print(f"   📊 Descriptor size: {descriptor_size}")
        print(f"   🌍 Spatial information: PRESERVED")
        print(f"   🔧 Layer strategy: Single layer (Layer 9 only)")
        print(f"   📐 Spatial downsampling: 4x (to match DINO feature map size)")

    def _init_weights(self):
        """初始化权重 - 简化版本，无需权重生成器"""
        pass

    def forward(self, sam_cls_token, sam_patch_token, mask=None):
        """
        前向传播：只使用SAM的第9层特征
        Args:
            sam_cls_token: [B, sam_dim, 1] SAM cls token (单层)
            sam_patch_token: [B, sam_dim, L] SAM patch token (单层)
            mask: [B, H, W] mask tensor，可选
        Returns:
            dict: {'cls_feature': cls_feature, 'pose_feature': pose_feature, 'pose_pooled': pose_pooled}
        """
        # 1. 处理SAM的cls token (单层，无需融合)
        cls_feature = sam_cls_token.squeeze(-1)  # [B, sam_dim]
        cls_feature = self.cls_projection(cls_feature)
        cls_feature = F.normalize(cls_feature, p=2, dim=-1)

        # 2. 处理SAM的patch token (单层，无需融合)
        # [B, C, L] -> [B, C, H, W]
        B, C, L = sam_patch_token.shape
        H = W = int(L ** 0.5)
        sam_spatial = sam_patch_token.view(B, C, H, W)  # [B, sam_dim, H, W]

        # 下采样4倍，与DINO特征图尺寸保持一致
        sam_spatial_downsampled = F.avg_pool2d(sam_spatial, kernel_size=4, stride=4)  # [B, sam_dim, H/4, W/4]

        # 投影到目标维度，保持空间结构
        pose_spatial = self.spatial_projection(sam_spatial_downsampled)  # [B, descriptor_size, H/4, W/4]

        # 特征图模式：对空间特征图进行逐像素归一化
        pose_feature = F.normalize(pose_spatial, p=2, dim=1)  # [B, descriptor_size, H, W]

        # 计算池化版本（用于对比和可视化）
        if mask is not None:
            pose_pooled = self._mask_guided_spatial_pooling(pose_spatial, mask)
        else:
            pose_pooled = pose_spatial.mean(dim=[2, 3])  # [B, descriptor_size]

        pose_pooled = F.normalize(pose_pooled, p=2, dim=-1)

        return {
            'cls_feature': cls_feature,  # [B, descriptor_size]
            'pose_feature': pose_feature,  # [B, D, H, W] - 特征图
            'pose_pooled': pose_pooled,  # [B, descriptor_size]
            'spatial_features': pose_spatial  # [B, descriptor_size, H, W]
        }

    def _mask_guided_spatial_pooling(self, spatial_features, mask):
        """使用mask引导的空间池化"""
        B, C, H, W = spatial_features.shape

        # 将mask缩放到特征图尺寸
        mask_resized = F.interpolate(
            mask.unsqueeze(1).float(),
            size=(H, W),
            mode='bilinear',
            align_corners=False
        ).squeeze(1)  # [B, H, W]

        # 空间加权平均池化
        mask_weights = mask_resized.unsqueeze(1)  # [B, 1, H, W]
        weighted_sum = torch.sum(spatial_features * mask_weights, dim=[2, 3])  # [B, descriptor_size]
        mask_sum = torch.sum(mask_weights, dim=[2, 3]) + 1e-6  # [B, 1]

        pooled_features = weighted_sum / mask_sum  # [B, descriptor_size]

        return pooled_features
class SpatialAwareSAMOnlyFeatureExtractor(nn.Module):
    """
    纯SAM空间感知特征提取器：
    - 只使用SAM特征，不依赖DINO
    - 使用SpatialAwareSAMOnlyAggregation进行特征聚合
    - 保持与其他特征提取器相同的接口
    """
    def __init__(self, config):
        super().__init__()
        self.config = config

        # 初始化SAM编码器
        print("🔧 Initializing EfficientSAM encoder for SAM-Only mode...")
        self.sam_encoder = build_efficient_sam_vitt()
        self.sam_encoder.eval()
        for p in self.sam_encoder.parameters():
            p.requires_grad = False

        # 获取特征维度
        descriptor_size = config.model.descriptor_size

        # 从配置中读取SAM特征维度，默认192
        sam_dim = getattr(config.model.efficient_sam, 'feature_dim', 192)

        # SAM-Only模式只使用第9层 (配置中的层数需要转换为0基索引)
        self.sam_layer_index = 9 - 1  # 第9层对应索引8

        print(f"🔧 Creating Spatial-Aware SAM-Only Feature Extractor:")
        print(f"   📊 SAM feature dimension: {sam_dim}")
        print(f"   📊 Output descriptor size: {descriptor_size}")
        print(f"   📊 SAM layer index: {self.sam_layer_index} (single layer)")
        print(f"   🌍 Spatial information: PRESERVED")

        # 使用纯SAM聚合网络
        self.aggregation_network = SpatialAwareSAMOnlyAggregation(
            sam_dim=sam_dim,
            descriptor_size=descriptor_size,
            device="cuda" if torch.cuda.is_available() else "cpu"
        )

    def extract_sam_features(self, x):
        """
        提取SAM第9层特征
        Args:
            x: 输入图像
        Returns:
            sam_cls_token: [B, sam_dim, 1] 第9层cls token
            sam_patch_token: [B, sam_dim, L] 第9层patch token
        """
        feature = None

        def sam_hook_fn(module, input, output):
            nonlocal feature
            # output: [B, H*W, C]
            feature = output  # [B, H*W, C]

        # 只在第9层注册hook
        total_blocks = len(self.sam_encoder.image_encoder.blocks)
        if self.sam_layer_index < total_blocks:
            hook = self.sam_encoder.image_encoder.blocks[self.sam_layer_index].register_forward_hook(sam_hook_fn)
        else:
            print(f"警告: SAM层索引 {self.sam_layer_index} 超出范围 (总共{total_blocks}层)，使用最后一层")
            hook = self.sam_encoder.image_encoder.blocks[-1].register_forward_hook(sam_hook_fn)

        with torch.no_grad():
            # 调整输入尺寸到SAM期望的分辨率
            x_resized = F.interpolate(x, size=(1024, 1024), mode='bilinear', align_corners=False)
            # SAM的前向传播
            _ = self.sam_encoder.image_encoder(x_resized)

        # 移除hook
        hook.remove()

        # 处理特征
        sam_patch_token = feature.transpose(1, 2)  # [B, C, L]
        sam_cls_token = feature.mean(dim=1, keepdim=True).transpose(1, 2)  # [B, C, 1]

        return sam_cls_token, sam_patch_token

    def forward(self, x, cad_feature=None, mask=None):
        """
        前向传播
        Args:
            x: 输入图像张量 [B, 3, H, W]
            cad_feature: CAD特征，在此版本中被忽略
            mask: [B, H, W] mask tensor，可选
        Returns:
            dict: {'cls_feature': cls_feature, 'pose_feature': pose_feature, 'spatial_features': spatial_features}
        """
        # 使用图像进行在线特征提取
        sam_cls_token, sam_patch_token = self.extract_sam_features(x)

        # 使用纯SAM聚合网络 (单层)
        features = self.aggregation_network(
            sam_cls_token=sam_cls_token,
            sam_patch_token=sam_patch_token,
            mask=mask
        )

        return features

    def calculate_similarity(self, query_features, template_features, feature_type='pose', mask=None):
        """
        计算特征相似度 - 复用DINO+SAM的逻辑
        Args:
            query_features: [B, D] 或 [B, D, H, W] 特征
            template_features: [B, D] 或 [B, D, H, W] 特征
            feature_type: 'pose' 或 'cls'
            mask: [B, H, W] mask tensor，可选，用于mask引导的相似度计算
        Returns:
            similarity: [B, B] 相似度矩阵
        """
        # 检查输入维度，判断是向量特征还是特征图
        if query_features.dim() == 2:  # [B, D] - 向量特征（cls或池化pose）
            return F.cosine_similarity(
                query_features.unsqueeze(1),  # [B, 1, D]
                template_features.unsqueeze(0),  # [1, B, D]
                dim=2
            )  # [B, B]

        elif query_features.dim() == 4:  # [B, D, H, W] - 特征图（空间pose）
            B, D, H, W = query_features.shape

            # 重塑为 [B, D, H*W]
            query_flat = query_features.view(B, D, -1)  # [B, D, H*W]
            template_flat = template_features.view(B, D, -1)  # [B, D, H*W]

            # 计算每个空间位置的相似度
            query_expanded = query_flat.unsqueeze(1)  # [B, 1, D, H*W]
            template_expanded = template_flat.unsqueeze(0)  # [1, B, D, H*W]

            # 计算空间相似度：在特征维度上计算余弦相似度
            spatial_similarities = F.cosine_similarity(
                query_expanded,  # [B, 1, D, H*W]
                template_expanded,  # [1, B, D, H*W]
                dim=2  # 在D维度上计算相似度
            )  # [B, B, H*W]

            # 如果提供了mask，使用mask引导的相似度计算
            if mask is not None:
                # 将mask缩放到特征图尺寸
                mask_resized = F.interpolate(
                    mask.unsqueeze(1).float(),  # [B, 1, H_img, W_img]
                    size=(H, W),
                    mode='bilinear',
                    align_corners=False
                ).squeeze(1)  # [B, H, W]

                # 重塑mask为 [B, H*W]
                mask_flat = mask_resized.view(B, -1)  # [B, H*W]

                # 扩展mask维度以匹配spatial_similarities: [B, B, H*W]
                mask_expanded = mask_flat.unsqueeze(1).expand(-1, B, -1)  # [B, B, H*W]

                # 应用mask权重到相似度计算
                masked_similarities = spatial_similarities * mask_expanded  # [B, B, H*W]

                # 计算加权平均相似度
                mask_sum = mask_expanded.sum(dim=2, keepdim=True) + 1e-6  # [B, B, 1]
                similarity = masked_similarities.sum(dim=2, keepdim=True) / mask_sum  # [B, B, 1]
                similarity = similarity.squeeze(-1)  # [B, B]
            else:
                # 对空间维度取平均，得到最终相似度
                similarity = spatial_similarities.mean(dim=2)  # [B, B]

            return similarity

        else:
            raise ValueError(f"Unsupported feature dimension: {query_features.dim()}. Expected 2D or 4D tensors.")


# ====== Cross-Modal DINO+SAM特征提取器 ======
class CrossModalDINO_SAM_FeatureExtractor(nn.Module):
    """
    Cross-Modal DINO+SAM特征提取器：
    - 使用CrossModalDINO_SAM_Aggregation进行高级跨模态特征融合
    - 支持Cross-Attention、Multi-Scale Fusion、Adaptive Gating等先进机制
    - 保持与原有特征提取器相同的接口
    """
    def __init__(self, config, dino_extractor):
        super().__init__()
        self.dino_extractor = dino_extractor
        self.config = config

        # 初始化SAM编码器
        print("🔧 Initializing EfficientSAM encoder for Cross-Modal fusion...")
        self.sam_encoder = build_efficient_sam_vitt()
        self.sam_encoder.eval()
        for p in self.sam_encoder.parameters():
            p.requires_grad = False

        # 获取特征维度
        feature_dim = dino_extractor.feature_dim  # DINO特征维度 (通常1024)
        descriptor_size = config.model.descriptor_size

        # 从配置中读取SAM特征维度，默认192
        sam_dim = getattr(config.model.efficient_sam, 'feature_dim', 192)

        print(f"🔧 Creating Cross-Modal DINO+SAM Feature Extractor:")
        print(f"   📊 DINO feature dimension: {feature_dim}")
        print(f"   📊 SAM feature dimension: {sam_dim}")
        print(f"   📊 Output descriptor size: {descriptor_size}")
        print(f"   🌍 Spatial information: PRESERVED")
        print(f"   🔄 Cross-Modal mechanisms: ENABLED")

        # 中等复杂度的Cross-Modal聚合网络
        self.aggregation_network = CrossModalDINO_SAM_Aggregation(
            feature_dim=feature_dim,
            sam_dim=sam_dim,
            descriptor_size=descriptor_size,
            device="cuda" if torch.cuda.is_available() else "cpu"
        )

    def extract_sam_features(self, x, target_layer_indices=None):
        """
        提取SAM特征，支持指定层数
        Args:
            x: 输入图像
            target_layer_indices: 目标层索引，如[9,10,11,12]，如果为None则提取所有层
        """
        if target_layer_indices is None:
            target_layer_indices = [8, 9, 10, 11]  # 默认与DINO对应的层 (0基索引)

        features = []

        def sam_hook_fn(module, input, output):
            # output: [B, H*W, C]
            features.append(output)  # [B, H*W, C] - 原始版本

        hooks = []
        # 只在目标层注册hook
        total_blocks = len(self.sam_encoder.image_encoder.blocks)
        for layer_idx in target_layer_indices:
            if layer_idx < total_blocks:
                hooks.append(
                    self.sam_encoder.image_encoder.blocks[layer_idx].register_forward_hook(sam_hook_fn)
                )
            else:
                print(f"警告: SAM层索引 {layer_idx} 超出范围 (总共{total_blocks}层)，使用最后一层")
                hooks.append(
                    self.sam_encoder.image_encoder.blocks[-1].register_forward_hook(sam_hook_fn)
                )

        with torch.no_grad():
            # 调整输入尺寸到SAM期望的分辨率
            x_resized = F.interpolate(x, size=(1024, 1024), mode='bilinear', align_corners=False)
            # SAM的前向传播
            _ = self.sam_encoder.image_encoder(x_resized)

        # 移除hooks
        for h in hooks:
            h.remove()

        # 处理特征
        sam_patch_tokens = features  # 直接使用捕获的特征
        sam_cls_tokens = [f.mean(dim=1, keepdim=True).transpose(1, 2) for f in sam_patch_tokens]  # [B, C, 1]

        # 转为[B, C, L]格式
        sam_patch_tokens = [f.transpose(1, 2) for f in sam_patch_tokens]  # [B, C, L]

        return sam_cls_tokens, sam_patch_tokens

    def forward(self, x, mask=None):
        """
        前向传播：提取DINO和SAM特征并进行Cross-Modal融合
        Args:
            x: 输入图像 [B, 3, H, W]
            mask: 可选的mask [B, H, W]
        Returns:
            dict: 包含cls_feature和pose_feature的字典
        """
        # 1. 提取DINO特征
        cls_tokens_raw, patch_tokens_raw = self.dino_extractor(x)

        # 转换DINO特征格式
        if isinstance(cls_tokens_raw, list) and len(cls_tokens_raw) > 0:
            if cls_tokens_raw[0].dim() == 2:  # [4, 768]
                # 重新组织：从B个[4, 768] -> 4个[B, 768]
                cls_tokens = []
                patch_tokens = []
                num_layers = cls_tokens_raw[0].size(0)  # 4
                for layer_idx in range(num_layers):
                    cls_layer = torch.stack([cls_tokens_raw[b][layer_idx] for b in range(len(cls_tokens_raw))], dim=0)
                    patch_layer = torch.stack([patch_tokens_raw[b][layer_idx] for b in range(len(patch_tokens_raw))], dim=0)
                    cls_layer = cls_layer.unsqueeze(-1)  # [B, 768, 1]
                    cls_tokens.append(cls_layer)
                    patch_tokens.append(patch_layer)
            else:
                cls_tokens = cls_tokens_raw
                patch_tokens = patch_tokens_raw
        else:
            cls_tokens = cls_tokens_raw
            patch_tokens = patch_tokens_raw

        # 2. 提取SAM特征 - 使用与DINO相同的层索引
        dino_layer_indices = self.config.model.feature_blocks['indices']
        # 转换为0基索引
        sam_layer_indices = [idx - 1 for idx in dino_layer_indices]
        sam_cls_tokens, sam_patch_tokens = self.extract_sam_features(x, sam_layer_indices)

        # 3. 使用中等复杂度的Cross-Modal聚合网络进行特征融合
        features = self.aggregation_network(
            cls_tokens=cls_tokens,
            patch_tokens=patch_tokens,
            sam_cls_tokens=sam_cls_tokens,
            sam_patch_tokens=sam_patch_tokens,
            mask=mask
        )

        return features

    def calculate_similarity(self, query_features, template_features, feature_type='pose', mask=None):
        """
        计算query和template之间的相似度矩阵
        Args:
            query_features: [B, D] 或 [B, D, H, W] 的query特征
            template_features: [N, D] 或 [N, D, H, W] 的template特征
            feature_type: 'pose' 或 'cls'，指定特征类型
            mask: [B, H, W] mask tensor，用于pose特征的mask引导相似度计算
        Returns:
            similarity_matrix: [B, N] 相似度矩阵
        """
        if query_features.dim() == 2 and template_features.dim() == 2:
            # 向量特征：[B, D] 和 [N, D]
            # 使用余弦相似度
            query_norm = F.normalize(query_features, p=2, dim=1)  # [B, D]
            template_norm = F.normalize(template_features, p=2, dim=1)  # [N, D]
            similarity_matrix = torch.mm(query_norm, template_norm.t())  # [B, N]
            return similarity_matrix

        elif query_features.dim() == 4 and template_features.dim() == 4:
            # 空间特征：[B, D, H, W] 和 [N, D, H, W]
            B, D, H, W = query_features.shape
            N = template_features.shape[0]

            # 归一化特征
            query_norm = F.normalize(query_features, p=2, dim=1)  # [B, D, H, W]
            template_norm = F.normalize(template_features, p=2, dim=1)  # [N, D, H, W]

            # 重塑为 [B, D, H*W] 和 [N, D, H*W]
            query_flat = query_norm.view(B, D, -1)  # [B, D, H*W]
            template_flat = template_norm.view(N, D, -1)  # [N, D, H*W]

            # 计算逐像素相似度：[B, N, H*W]
            spatial_similarities = torch.einsum('bdi,ndi->bni', query_flat, template_flat)

            if mask is not None and feature_type == 'pose':
                # 使用mask进行加权平均
                # 将mask缩放到特征图尺寸
                mask_resized = F.interpolate(
                    mask.unsqueeze(1).float(),
                    size=(H, W),
                    mode='bilinear',
                    align_corners=False
                ).squeeze(1)  # [B, H, W]

                # 展平mask并扩展维度
                mask_flat = mask_resized.view(B, 1, -1)  # [B, 1, H*W]
                mask_expanded = mask_flat.expand(-1, N, -1)  # [B, N, H*W]

                # 加权平均：只计算mask对应部分的相似度
                weighted_sum = torch.sum(spatial_similarities * mask_expanded, dim=2)  # [B, N]
                mask_sum = torch.sum(mask_expanded, dim=2) + 1e-6  # [B, N] 避免除零
                similarity_matrix = weighted_sum / mask_sum  # [B, N]
            else:
                # 对空间维度取平均，得到最终相似度
                similarity_matrix = spatial_similarities.mean(dim=2)  # [B, N]

            return similarity_matrix

        else:
            raise ValueError(f"Unsupported feature dimensions: query {query_features.dim()}D, template {template_features.dim()}D. Expected both 2D or both 4D tensors.")
# ====== 辅助类定义 ======
class PoseHead(nn.Module):
    """Pose特征处理Head - 保持现有的复杂逻辑"""
    def __init__(self, unified_dim, descriptor_size, device="cuda"):
        super().__init__()
        self.unified_dim = unified_dim
        self.descriptor_size = descriptor_size

        # 双重注意力机制
        self.spatial_attention = nn.Sequential(
            nn.Conv2d(unified_dim * 2, unified_dim // 4, 3, padding=1),
            nn.ReLU(),
            nn.Conv2d(unified_dim // 4, 1, 1),
            nn.Sigmoid()
        ).to(device)

        self.channel_attention = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(unified_dim * 2, unified_dim // 2, 1),
            nn.ReLU(),
            nn.Conv2d(unified_dim // 2, unified_dim * 2, 1),
            nn.Sigmoid()
        ).to(device)

        # 多尺度融合
        self.multi_scale_conv = nn.ModuleList([
            nn.Conv2d(unified_dim * 2, unified_dim, kernel_size=k, padding=k//2)
            for k in [1, 3]
        ]).to(device)

        # 最终投影
        self.final_projection = nn.Sequential(
            nn.Conv2d(unified_dim * 2, unified_dim, 1),
            nn.BatchNorm2d(unified_dim),
            nn.ReLU(),
            nn.Conv2d(unified_dim, descriptor_size, 1)
        ).to(device)

    def forward(self, shared_features):
        """
        Args:
            shared_features: [B, unified_dim, H, W]
        Returns:
            pose_feature: [B, descriptor_size, H, W]
        """
        # 双重注意力
        dual_features = torch.cat([shared_features, shared_features], dim=1)  # [B, 2*unified_dim, H, W]

        # Channel attention
        channel_att = self.channel_attention(dual_features)  # [B, 2*unified_dim, 1, 1]
        # Spatial attention
        spatial_att = self.spatial_attention(dual_features)  # [B, 1, H, W]

        # 应用注意力
        attended_features = dual_features * channel_att * spatial_att

        # 多尺度融合
        multi_scale_features = []
        for conv in self.multi_scale_conv:
            scale_feature = conv(attended_features)
            multi_scale_features.append(scale_feature)

        # 拼接多尺度特征
        multi_scale_fused = torch.cat(multi_scale_features, dim=1)  # [B, 2*unified_dim, H, W]

        # 最终投影
        pose_feature = self.final_projection(multi_scale_fused)  # [B, descriptor_size, H, W]

        return F.normalize(pose_feature, p=2, dim=1)


class CLSHead(nn.Module):
    """CLS特征处理Head - 带残差连接的设计"""
    def __init__(self, unified_dim, descriptor_size, device="cuda"):
        super().__init__()
        self.unified_dim = unified_dim
        self.descriptor_size = descriptor_size

        # 空间池化
        self.spatial_pooling = nn.AdaptiveAvgPool2d(1)

        # CLS特征融合
        self.cls_fusion = nn.Sequential(
            nn.Linear(unified_dim + unified_dim, unified_dim),  # 池化特征 + CLS tokens
            nn.ReLU(),
            nn.Linear(unified_dim, descriptor_size)
        ).to(device)

        # CLS tokens的直接投影（用于残差连接）
        self.cls_residual_projection = nn.Linear(unified_dim, descriptor_size).to(device)

    def forward(self, shared_features, cls_tokens_fused):
        """
        Args:
            shared_features: [B, unified_dim, H, W] 共享的空间特征
            cls_tokens_fused: [B, unified_dim] 融合后的CLS tokens
        Returns:
            cls_feature: [B, descriptor_size]
        """
        # 空间池化
        pooled_spatial = self.spatial_pooling(shared_features).squeeze(-1).squeeze(-1)  # [B, unified_dim]

        # 与CLS tokens融合
        combined = torch.cat([pooled_spatial, cls_tokens_fused], dim=1)  # [B, 2*unified_dim]
        fused_cls_feature = self.cls_fusion(combined)  # [B, descriptor_size]

        # 残差连接：融合特征 + 原始CLS tokens投影
        cls_residual = self.cls_residual_projection(cls_tokens_fused)  # [B, descriptor_size]
        cls_feature = fused_cls_feature + cls_residual  # [B, descriptor_size]

        return F.normalize(cls_feature, p=2, dim=-1)


class CrossModalDINO_SAM_Aggregation(nn.Module):
    """
    改进的Cross-Modal DINO+SAM聚合器：
    - 共享的跨模态交互和层间聚合
    - 双Head设计：Pose Head + CLS Head
    - 参数高效且逻辑简洁
    """
    def __init__(self, feature_dim, sam_dim, descriptor_size=16, device="cuda"):
        super().__init__()
        self.feature_dim = feature_dim  # 1024
        self.sam_dim = sam_dim  # 192
        self.descriptor_size = descriptor_size
        self.device = device

        # 统一维度
        self.unified_dim = 256

        # 1. 共享的特征投影
        self.dino_projection = nn.Sequential(
            nn.Linear(feature_dim, self.unified_dim),
            nn.LayerNorm(self.unified_dim),
            nn.ReLU()
        ).to(device)

        self.sam_projection = nn.Sequential(
            nn.Linear(sam_dim, self.unified_dim),
            nn.LayerNorm(self.unified_dim),
            nn.ReLU()
        ).to(device)

        # 2. 共享的自适应门控
        self.feature_gate = nn.Sequential(
            nn.Linear(self.unified_dim * 2, self.unified_dim // 2),
            nn.ReLU(),
            nn.Linear(self.unified_dim // 2, self.unified_dim),
            nn.Sigmoid()
        ).to(device)

        # 3. 共享的层间加权聚合
        num_layers = 4
        self.layer_weights = nn.Parameter(torch.ones(num_layers))

        # 4. CLS tokens处理（简化版本）
        self.cls_token_projection = nn.Sequential(
            nn.Linear(self.unified_dim, self.unified_dim),
            nn.ReLU()
        ).to(device)

        # 5. Pose Head - 保持现有逻辑
        self.pose_head = PoseHead(self.unified_dim, descriptor_size, device)

        # 6. CLS Head - 新设计
        self.cls_head = CLSHead(self.unified_dim, descriptor_size, device)

        print(f"✅ Improved CrossModal Aggregation initialized:")
        print(f"   📊 Unified dimension: {self.unified_dim}")
        print(f"   🏗️ Architecture: Shared Fusion + Dual Heads")
        print(f"   🎯 Pose Head: Spatial attention + Multi-scale fusion")
        print(f"   🏷️ CLS Head: Spatial pooling + Token fusion")

    def _simple_cross_modal_fusion(self, dino_features, sam_features):
        """
        简化的Cross-Modal特征融合 (无Attention)
        Args:
            dino_features: [B, L, feature_dim] DINO features
            sam_features: [B, L, sam_dim] SAM features
        Returns:
            aligned_dino: [B, L, unified_dim] aligned DINO features (projected)
            aligned_sam: [B, L, unified_dim] aligned SAM features (projected)
        """
        # 将DINO特征投影到统一维度空间 (1024 -> 256)
        if len(dino_features.shape) == 3:  # [B, L, feature_dim]
            B, L, _ = dino_features.shape
            dino_features_flat = dino_features.contiguous().view(-1, dino_features.shape[-1])  # [B*L, feature_dim]
            dino_projected_flat = self.dino_projection(dino_features_flat)  # [B*L, unified_dim]
            dino_projected = dino_projected_flat.view(B, L, -1)  # [B, L, unified_dim]
        else:  # [B, feature_dim]
            dino_projected = self.dino_projection(dino_features)  # [B, unified_dim]
            if len(sam_features.shape) == 3:
                dino_projected = dino_projected.unsqueeze(1)  # [B, 1, unified_dim]

        # 将SAM特征也投影到统一维度空间 (192 -> 256)
        if len(sam_features.shape) == 3:  # [B, L, sam_dim]
            B, L, _ = sam_features.shape
            sam_features_flat = sam_features.contiguous().view(-1, sam_features.shape[-1])  # [B*L, sam_dim]
            sam_projected_flat = self.sam_projection(sam_features_flat)  # [B*L, unified_dim]
            sam_projected = sam_projected_flat.view(B, L, -1)  # [B, L, unified_dim]
        else:  # [B, sam_dim]
            sam_projected = self.sam_projection(sam_features)  # [B, unified_dim]
            if len(dino_features.shape) == 3:
                sam_projected = sam_projected.unsqueeze(1)  # [B, 1, unified_dim]

        return dino_projected, sam_projected

    def _adaptive_feature_gating(self, dino_features, sam_features):
        """
        Adaptive Feature Gating for dynamic feature combination
        Args:
            dino_features: [B, L, unified_dim] or [B, unified_dim, H, W]
            sam_features: [B, L, unified_dim] or [B, unified_dim, H, W]
        Returns:
            gated_features: [B, L, unified_dim] or [B, unified_dim, H, W]
        """
        if len(dino_features.shape) == 4:  # Spatial features [B, C, H, W]
            B, C, H, W = dino_features.shape
            # Flatten for gating computation
            dino_flat = dino_features.view(B, C, -1).transpose(1, 2)  # [B, H*W, C]
            sam_flat = sam_features.view(B, C, -1).transpose(1, 2)    # [B, H*W, C]

            # Compute gates
            combined = torch.cat([dino_flat, sam_flat], dim=-1)  # [B, H*W, 2*C]
            gates = self.feature_gate(combined)  # [B, H*W, C]

            # Apply gating
            gated_flat = gates * dino_flat + (1 - gates) * sam_flat  # [B, H*W, C]
            gated_features = gated_flat.transpose(1, 2).contiguous().view(B, C, H, W)  # [B, C, H, W]
        else:  # Sequential features [B, L, C]
            combined = torch.cat([dino_features, sam_features], dim=-1)  # [B, L, 2*C]
            gates = self.feature_gate(combined)  # [B, L, C]
            gated_features = gates * dino_features + (1 - gates) * sam_features  # [B, L, C]

        return gated_features

    def _process_cls_tokens(self, cls_tokens, sam_cls_tokens):
        """
        处理CLS tokens的简化版本
        Args:
            cls_tokens: list of [B, feature_dim, 1] DINO cls tokens
            sam_cls_tokens: list of [B, sam_dim, 1] SAM cls tokens
        Returns:
            cls_tokens_fused: [B, unified_dim] 融合后的CLS特征
        """
        cls_features = []
        for dino_cls, sam_cls in zip(cls_tokens, sam_cls_tokens):
            # Reshape for processing: [B, 1, C]
            dino_cls_seq = dino_cls.transpose(1, 2)  # [B, 1, feature_dim]
            sam_cls_seq = sam_cls.transpose(1, 2)    # [B, 1, sam_dim]

            # 跨模态融合
            enhanced_dino_cls, enhanced_sam_cls = self._simple_cross_modal_fusion(
                dino_cls_seq, sam_cls_seq
            )

            # 自适应门控
            gated_cls = self._adaptive_feature_gating(enhanced_dino_cls, enhanced_sam_cls)
            cls_features.append(gated_cls.squeeze(1))  # [B, unified_dim]

        # 层间加权聚合
        cls_stack = torch.stack(cls_features, dim=0)  # [num_layers, B, unified_dim]
        layer_weights = F.softmax(self.layer_weights, dim=0)
        cls_fused = torch.sum(cls_stack * layer_weights.view(-1, 1, 1), dim=0)  # [B, unified_dim]

        # 简单投影
        cls_fused = self.cls_token_projection(cls_fused)  # [B, unified_dim]

        return cls_fused

    def _process_shared_spatial_features(self, patch_tokens, sam_patch_tokens):
        """
        处理共享的空间特征
        Args:
            patch_tokens: list of [B, feature_dim, L] DINO patch tokens
            sam_patch_tokens: list of [B, sam_dim, L] SAM patch tokens
        Returns:
            shared_features: [B, unified_dim, H, W] 共享的空间特征
        """
        spatial_features = []
        for dino_patch, sam_patch in zip(patch_tokens, sam_patch_tokens):
            # Convert to spatial format
            B, C_dino, L_dino = dino_patch.shape
            H_dino = W_dino = int(L_dino ** 0.5)
            dino_map = dino_patch.view(B, C_dino, H_dino, W_dino)  # [B, feature_dim, H, W]

            B, C_sam, L_sam = sam_patch.shape
            H_sam = W_sam = int(L_sam ** 0.5)
            sam_map = sam_patch.view(B, C_sam, H_sam, W_sam)  # [B, sam_dim, H, W]

            # Resize SAM to match DINO spatial dimensions
            if (H_sam, W_sam) != (H_dino, W_dino):
                sam_map = F.interpolate(
                    sam_map, size=(H_dino, W_dino),
                    mode='bilinear', align_corners=False
                )

            # 跨模态融合 on flattened spatial features
            dino_seq = dino_map.view(B, C_dino, -1).transpose(1, 2)  # [B, H*W, feature_dim]
            sam_seq = sam_map.view(B, C_sam, -1).transpose(1, 2)     # [B, H*W, sam_dim]

            enhanced_dino_seq, enhanced_sam_seq = self._simple_cross_modal_fusion(dino_seq, sam_seq)

            # Convert back to spatial format
            enhanced_dino_map = enhanced_dino_seq.transpose(1, 2).contiguous().view(B, self.unified_dim, H_dino, W_dino)
            enhanced_sam_map = enhanced_sam_seq.transpose(1, 2).contiguous().view(B, self.unified_dim, H_dino, W_dino)

            # 自适应门控
            gated_spatial = self._adaptive_feature_gating(enhanced_dino_map, enhanced_sam_map)
            spatial_features.append(gated_spatial)

        # 层间加权聚合
        spatial_stack = torch.stack(spatial_features, dim=0)  # [num_layers, B, unified_dim, H, W]
        layer_weights = F.softmax(self.layer_weights, dim=0)
        shared_features = torch.sum(spatial_stack * layer_weights.view(-1, 1, 1, 1, 1), dim=0)  # [B, unified_dim, H, W]

        return shared_features

    def forward(self, cls_tokens, patch_tokens, sam_cls_tokens, sam_patch_tokens, mask=None):
        """
        改进的Cross-Modal Forward Pass - 共享特征 + 双Head设计
        Args:
            cls_tokens: list of [B, feature_dim, 1] DINO cls tokens
            patch_tokens: list of [B, feature_dim, L] DINO patch tokens
            sam_cls_tokens: list of [B, sam_dim, 1] SAM cls tokens
            sam_patch_tokens: list of [B, sam_dim, L] SAM patch tokens
            mask: [B, H, W] mask tensor，可选
        Returns:
            dict: {'cls_feature': cls_feature, 'pose_feature': pose_feature, 'pose_pooled': pose_pooled}
        """
        # 1. 共享的跨模态交互和层间聚合（只处理patch tokens）
        shared_features = self._process_shared_spatial_features(patch_tokens, sam_patch_tokens)  # [B, unified_dim, H, W]

        # 2. 单独处理CLS tokens（简化版本）
        cls_tokens_fused = self._process_cls_tokens(cls_tokens, sam_cls_tokens)  # [B, unified_dim]

        # 3. 双Head分别处理
        pose_feature = self.pose_head(shared_features)  # [B, descriptor_size, H, W]
        cls_feature = self.cls_head(shared_features, cls_tokens_fused)  # [B, descriptor_size]

        # 4. 计算pooled版本
        if mask is not None:
            pose_pooled = self._mask_guided_spatial_pooling(pose_feature, mask)
        else:
            pose_pooled = pose_feature.mean(dim=[2, 3])  # [B, descriptor_size]

        pose_pooled = F.normalize(pose_pooled, p=2, dim=-1)

        return {
            'cls_feature': cls_feature,      # [B, descriptor_size]
            'pose_feature': pose_feature,    # [B, descriptor_size, H, W]
            'pose_pooled': pose_pooled,      # [B, descriptor_size]
            'spatial_features': pose_feature # [B, descriptor_size, H, W]
        }

    def _mask_guided_spatial_pooling(self, spatial_features, mask):
        """使用mask引导的空间池化"""
        B, C, H, W = spatial_features.shape

        # 将mask缩放到特征图尺寸
        mask_resized = F.interpolate(
            mask.unsqueeze(1).float(),
            size=(H, W),
            mode='bilinear',
            align_corners=False
        ).squeeze(1)  # [B, H, W]

        # 空间加权平均池化
        mask_weights = mask_resized.unsqueeze(1)  # [B, 1, H, W]
        weighted_sum = torch.sum(spatial_features * mask_weights, dim=[2, 3])  # [B, descriptor_size]
        mask_sum = torch.sum(mask_weights, dim=[2, 3]) + 1e-6  # [B, 1]

        pooled_features = weighted_sum / mask_sum  # [B, descriptor_size]

        return pooled_features

    def calculate_similarity(self, query_features, template_features, feature_type='pose', mask=None):
        """
        计算query和template之间的相似度矩阵
        Args:
            query_features: [B, D] 或 [B, D, H, W] 的query特征
            template_features: [N, D] 或 [N, D, H, W] 的template特征
            feature_type: 'pose' 或 'cls'，指定特征类型
            mask: [B, H, W] mask tensor，用于pose特征的mask引导相似度计算
        Returns:
            similarity_matrix: [B, N] 相似度矩阵
        """
        if query_features.dim() == 2 and template_features.dim() == 2:
            # 向量特征：[B, D] 和 [N, D]
            # 使用余弦相似度
            query_norm = F.normalize(query_features, p=2, dim=1)  # [B, D]
            template_norm = F.normalize(template_features, p=2, dim=1)  # [N, D]
            similarity_matrix = torch.mm(query_norm, template_norm.t())  # [B, N]
            return similarity_matrix

        elif query_features.dim() == 4 and template_features.dim() == 4:
            # 空间特征：[B, D, H, W] 和 [N, D, H, W]
            B, D, H, W = query_features.shape
            N = template_features.shape[0]

            # 归一化特征
            query_norm = F.normalize(query_features, p=2, dim=1)  # [B, D, H, W]
            template_norm = F.normalize(template_features, p=2, dim=1)  # [N, D, H, W]

            # 重塑为 [B, D, H*W] 和 [N, D, H*W]
            query_flat = query_norm.view(B, D, -1)  # [B, D, H*W]
            template_flat = template_norm.view(N, D, -1)  # [N, D, H*W]

            # 计算逐像素相似度：[B, N, H*W]
            spatial_similarities = torch.einsum('bdi,ndi->bni', query_flat, template_flat)

            if mask is not None and feature_type == 'pose':
                # 使用mask进行加权平均
                # 将mask缩放到特征图尺寸
                mask_resized = F.interpolate(
                    mask.unsqueeze(1).float(),
                    size=(H, W),
                    mode='bilinear',
                    align_corners=False
                ).squeeze(1)  # [B, H, W]

                # 展平mask并扩展维度
                mask_flat = mask_resized.view(B, 1, -1)  # [B, 1, H*W]
                mask_expanded = mask_flat.expand(-1, N, -1)  # [B, N, H*W]

                # 加权平均：只计算mask对应部分的相似度
                weighted_sum = torch.sum(spatial_similarities * mask_expanded, dim=2)  # [B, N]
                mask_sum = torch.sum(mask_expanded, dim=2) + 1e-6  # [B, N] 避免除零
                similarity_matrix = weighted_sum / mask_sum  # [B, N]
            else:
                # 对空间维度取平均，得到最终相似度
                similarity_matrix = spatial_similarities.mean(dim=2)  # [B, N]

            return similarity_matrix

        else:
            raise ValueError(f"Unsupported feature dimensions: query {query_features.dim()}D, template {template_features.dim()}D. Expected both 2D or both 4D tensors.")
