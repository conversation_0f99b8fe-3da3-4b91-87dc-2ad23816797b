import os
import shutil
from pathlib import Path

# 源目录和目标目录
src_root = '/data1/dataset/nocs/data/Real/test'
dst_dir = './color_images'

# 创建目标目录
os.makedirs(dst_dir, exist_ok=True)

# 统计信息
total_copied = 0
errors = []

print(f"开始处理源目录: {src_root}")
try:
    # 检查源目录是否存在
    if not os.path.exists(src_root):
        raise Exception(f"源目录不存在: {src_root}")
    
    # 获取并打印所有场景文件夹
    scene_dirs = os.listdir(src_root)
    print(f"找到以下场景文件夹: {scene_dirs}")
    
    # 遍历所有场景文件夹
    for scene_dir in scene_dirs:
        scene_path = os.path.join(src_root, scene_dir)
        print(f"\n处理场景: {scene_dir}")
        
        if not os.path.isdir(scene_path):
            print(f"跳过非目录文件: {scene_dir}")
            continue
            
        try:
            # 检查是否有权限访问场景目录
            if not os.access(scene_path, os.R_OK):
                raise Exception(f"没有权限访问目录: {scene_path}")
            
            # 遍历场景文件夹中的所有文件
            files = os.listdir(scene_path)
            color_files = [f for f in files if f.endswith('_color.png')]
            print(f"在 {scene_dir} 中找到 {len(color_files)} 个颜色图片")
            
            for file_name in color_files:
                try:
                    # 构建源文件和目标文件的完整路径
                    src_file = os.path.join(scene_path, file_name)
                    dst_file = os.path.join(dst_dir, f"{scene_dir}_{file_name}")
                    
                    # 复制文件
                    shutil.copy2(src_file, dst_file)
                    total_copied += 1
                    print(f"已复制: {file_name}")
                except Exception as e:
                    error_msg = f"复制文件 {file_name} 时出错: {str(e)}"
                    print(error_msg)
                    errors.append(error_msg)
                    
        except Exception as e:
            error_msg = f"处理场景 {scene_dir} 时出错: {str(e)}"
            print(error_msg)
            errors.append(error_msg)

except Exception as e:
    error_msg = f"主程序出错: {str(e)}"
    print(error_msg)
    errors.append(error_msg)

# 打印最终统计信息
print("\n========== 复制完成 ==========")
print(f"总共成功复制: {total_copied} 个文件")
if errors:
    print("\n发生的错误:")
    for error in errors:
        print(f"- {error}") 