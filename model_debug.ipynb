#%%
import argparse
import os
import torch
import datetime
import wandb
import torch.nn as nn
import logging

from lib.utils import weights, metrics, logger
from lib.utils.optimizer import adjust_learning_rate
from lib.datasets.dataloader_utils import init_dataloader
from lib.utils.config import Config

from lib.models.dino_network import DINOv2Extractor
from lib.models.dinov2.aggregation_network import AggregationNetwork
from lib.models.dino_feature_network import DINOv2FeatureExtractor

from lib.datasets.linemod.dataloader_query_sam import LINEMODMultiQuery
from lib.datasets.linemod.dataloader_template_sam import TemplatesLINEMODMultiQuery
from lib.datasets.im_transform import im_transform
from lib.datasets.linemod import inout
from lib.datasets.linemod import training_utils_dino, testing_utils_dino
import shutup
shutup.please()

# 设置CUDA设备
os.environ['CUDA_VISIBLE_DEVICES'] = '0'

parser = argparse.ArgumentParser()
# parser.add_argument('--split', type=str, choices=['split1', 'split2', 'split3'])
args = parser.parse_known_args()

# 加载配置
config_global = Config(config_file="./config.json").get_config()
config_run = Config('./config_run/LM_DINO_split1.json').get_config()


# 初始化模型
dino_extractor = DINOv2Extractor(
    device="cuda",
    output_resolution=config_run.model.output_resolution,
    pose_tokens_config=config_run.model.pose_tokens,
    feature_blocks_config=config_run.model.feature_blocks,
    version=config_run.model.version
)

# 获取特征维度
feature_dim = dino_extractor.feature_dim
num_blocks = len(config_run.model.feature_blocks['indices'])
feature_dims = [feature_dim] * num_blocks

# 初始化聚合网络
aggregation_network = AggregationNetwork(
    descriptor_size=config_run.model.descriptor_size,
    feature_dims=feature_dims,
    device="cuda",
    input_dim=feature_dim,
    use_pose_tokens=config_run.model.pose_tokens.get('use_attention', False)  # 是否使用pose tokens进行自注意力
)
model = DINOv2FeatureExtractor(
    config=config_run,
    threshold=0.2,
    dino_extractor=dino_extractor,
    aggregation_network=aggregation_network,
)



#%%
a = torch.load('/home/<USER>/projects/RJL2025/my-diff-feats-pose/dataset/results/weights/LM_DINO_split1_20250312_113113/model_epoch17.pth')
#%%
a.keys()
#%%
learnable_tokens = a['learnable_tokens'].detach().cpu()
#%%
import matplotlib.pyplot as plt
x1,x2,x3,x4,x5 = learnable_tokens[0],learnable_tokens[1],learnable_tokens[2],learnable_tokens[3],learnable_tokens[4]

# 计算相似度矩阵
tokens = torch.stack([x1,x2,x3,x4,x5])
similarity_matrix = torch.nn.functional.cosine_similarity(tokens.unsqueeze(1), tokens.unsqueeze(0), dim=2)

# 绘制相似度矩阵
plt.figure(figsize=(8,6))
plt.imshow(similarity_matrix, cmap='YlOrRd')

# 在每个格子中添加相似度值
for i in range(5):
    for j in range(5):
        plt.text(j, i, f'{similarity_matrix[i,j]:.2f}', 
                ha='center', va='center')

plt.colorbar()
plt.title('Token Similarity Matrix')
plt.xlabel('Token Index')
plt.ylabel('Token Index')
plt.tight_layout()
plt.show()