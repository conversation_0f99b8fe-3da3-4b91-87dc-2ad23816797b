#!/usr/bin/env python
"""
调试CLS特征处理问题
"""

import torch
import torch.nn.functional as F
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from lib.models.simple_dino_feature_network import SpatialAwareSimplifiedDINOAggregation

def debug_cls_processing():
    """调试CLS特征处理"""
    print("🔍 调试CLS特征处理问题...")
    
    # 模拟单层配置的参数
    batch_size = 16
    feature_dim = 768
    num_layers = 1  # 单层配置
    device = "cuda" if torch.cuda.is_available() else "cpu"
    
    print(f"📊 参数：batch_size={batch_size}, feature_dim={feature_dim}, num_layers={num_layers}")
    
    # 创建聚合网络
    aggregation_net = SpatialAwareSimplifiedDINOAggregation(
        feature_dim=feature_dim,
        descriptor_size=16,
        device=device,
        use_spatial_features=True,
        num_layers=num_layers
    )
    
    # 创建模拟输入 - 模拟DINO提取器的输出
    cls_tokens = []
    patch_tokens = []
    
    for i in range(num_layers):
        # CLS特征: [B, feature_dim, 1] - 这是DINO提取器的输出格式
        cls_token = torch.randn(batch_size, feature_dim, 1, device=device)
        cls_tokens.append(cls_token)
        print(f"   CLS Token {i}: {cls_token.shape}")
        
        # Patch特征: [B, feature_dim, 256]
        patch_token = torch.randn(batch_size, feature_dim, 256, device=device)
        patch_tokens.append(patch_token)
    
    print(f"\n🔍 开始处理CLS特征...")
    
    # 手动执行聚合网络中的CLS处理步骤
    cls_features = []
    for i, cls_token in enumerate(cls_tokens):
        print(f"   处理CLS Token {i}: {cls_token.shape}")
        cls_squeezed = cls_token.squeeze(-1)  # [B, feature_dim]
        print(f"   Squeeze后: {cls_squeezed.shape}")
        cls_features.append(cls_squeezed)
    
    # Stack操作
    cls_stack = torch.stack(cls_features, dim=0)  # [num_layers, B, feature_dim]
    print(f"   CLS Stack: {cls_stack.shape}")
    
    # 创建层权重
    layer_weights = F.softmax(torch.ones(num_layers, device=device), dim=0)
    print(f"   Layer weights: {layer_weights.shape}")
    
    # 加权融合
    try:
        weight_reshaped = layer_weights.view(-1, 1, 1)  # [num_layers, 1, 1]
        print(f"   Weight reshaped: {weight_reshaped.shape}")
        
        weighted = cls_stack * weight_reshaped
        print(f"   Weighted: {weighted.shape}")
        
        cls_fused = torch.sum(weighted, dim=0)
        print(f"   CLS Fused: {cls_fused.shape}")
        
        print("✅ CLS特征处理成功！")
        
        # 测试完整的forward
        print(f"\n🧪 测试完整的forward...")
        with torch.no_grad():
            features = aggregation_net(cls_tokens, patch_tokens)
        print(f"✅ Forward成功完成")
        print(f"   CLS特征: {features['cls_feature'].shape}")
        print(f"   Pose特征: {features['pose_feature'].shape}")
        
    except Exception as e:
        print(f"❌ CLS特征处理失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_cls_processing() 