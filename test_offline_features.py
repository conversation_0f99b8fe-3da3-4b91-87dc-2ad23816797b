#!/usr/bin/env python3
"""
测试离线特征加载功能的脚本
"""

import os
import sys
import json
import torch
from PIL import Image
import numpy as np

# 添加lib目录到路径
sys.path.append('./lib')
sys.path.append('./lib/datasets')

from lib.datasets.linemod.dataloader_dino import LINEMODQuery, TemplatesLINEMOD

def test_offline_features():
    print("开始测试离线特征加载功能...")
    
    # 加载配置文件
    config_path = "config_run/LM_DINO_split1.json"
    with open(config_path, 'r') as f:
        config = json.load(f)
    
    # 启用离线特征
    config['model']['offline_features']['enabled'] = True
    print(f"离线特征根目录: {config['model']['offline_features']['feature_root']}")
    
    # 设置测试参数
    root_dir = "./dataset"
    list_id_obj = [0]  # 只测试一个物体
    
    print("创建数据加载器...")
    # 创建查询数据集
    query_dataset = LINEMODQuery(
        root_dir=root_dir,
        dataset="LINEMOD",
        list_id_obj=list_id_obj,
        split="seen_test",
        image_size=512,
        save_path="test_samples",
        use_normalize=False,
        use_mask=False,
        gt_bbox_known=True,
        use_aug=False,
        num_negatives=1,
        use_cad_feature=True,
        offline_features_config=config['model']['offline_features']
    )
    
    # 创建模板数据集
    template_dataset = TemplatesLINEMOD(
        root_dir=root_dir,
        dataset="templatesLINEMOD",
        list_id_obj=list_id_obj,
        split="test",
        image_size=512,
        save_path="test_samples",
        use_normalize=False,
        use_cad_feature=True,
        offline_features_config=config['model']['offline_features']
    )
    
    print(f"查询数据集长度: {len(query_dataset)}")
    print(f"模板数据集长度: {len(template_dataset)}")
    
    if len(query_dataset) > 0:
        print("\n测试查询数据加载...")
        try:
            sample = query_dataset[0]
            print(f"查询样本键: {sample.keys()}")
            
            if 'query' in sample:
                query_data = sample['query']
                if isinstance(query_data, dict) and query_data.get('type') == 'features':
                    print("✓ 成功加载离线特征!")
                    dino_features = query_data['dino']
                    sam_features = query_data['sam']
                    print(f"  DINO特征形状: cls_tokens={dino_features['cls_tokens'].shape}, patch_tokens={dino_features['patch_tokens'].shape}")
                    print(f"  SAM特征形状: {sam_features.shape}")
                elif isinstance(query_data, torch.Tensor):
                    print("○ 使用图像模式 (离线特征可能不存在)")
                    print(f"  图像张量形状: {query_data.shape}")
                else:
                    print(f"? 未知查询数据类型: {type(query_data)}")
            
            if 'cad_feature' in sample:
                print(f"  CAD特征形状: {sample['cad_feature'].shape}")
                
        except Exception as e:
            print(f"✗ 查询数据加载失败: {e}")
    
    if len(template_dataset) > 0:
        print("\n测试模板数据加载...")
        try:
            sample = template_dataset[0]
            print(f"模板样本键: {sample.keys()}")
            
            if 'template' in sample:
                template_data = sample['template']
                if isinstance(template_data, dict) and template_data.get('type') == 'features':
                    print("✓ 成功加载模板离线特征!")
                    dino_features = template_data['dino']
                    sam_features = template_data['sam']
                    print(f"  DINO特征形状: cls_tokens={dino_features['cls_tokens'].shape}, patch_tokens={dino_features['patch_tokens'].shape}")
                    print(f"  SAM特征形状: {sam_features.shape}")
                elif isinstance(template_data, torch.Tensor):
                    print("○ 使用图像模式 (离线特征可能不存在)")
                    print(f"  图像张量形状: {template_data.shape}")
                else:
                    print(f"? 未知模板数据类型: {type(template_data)}")
                    
        except Exception as e:
            print(f"✗ 模板数据加载失败: {e}")
    
    print("\n测试完成!")

def test_feature_path_construction():
    """测试特征路径构建功能"""
    print("测试特征路径构建功能...")
    
    # 创建一个临时数据集实例来测试方法，但不调用get_data
    class TestDataset:
        def __init__(self, dataset_name="LINEMOD"):
            self.feature_root = "/data1/dataset/renjl"
            self.dataset_name = dataset_name
            
        def _construct_feature_paths(self, image_path):
            # 复制数据集中的逻辑
            path_parts = image_path.split(os.sep)
            
            if "templatesLINEMOD" in image_path:
                obj_name = path_parts[-2]
                filename = os.path.splitext(path_parts[-1])[0]
                if "train" in image_path:
                    feature_dir = os.path.join(self.feature_root, "templatesLINEMOD", "train", obj_name)
                else:
                    feature_dir = os.path.join(self.feature_root, "templatesLINEMOD", "test", obj_name)
            elif any(dataset in image_path for dataset in ["LINEMOD", "occlusionLINEMOD"]):
                dataset_name = "occlusionLINEMOD" if "occlusionLINEMOD" in image_path else "LINEMOD"
                obj_name = path_parts[-3]
                filename = os.path.splitext(path_parts[-1])[0]
                feature_dir = os.path.join(self.feature_root, dataset_name, obj_name)
            elif len(path_parts) == 2 and path_parts[0] and path_parts[1].endswith('.png'):
                # 处理简化格式: obj_name/000000.png
                obj_name = path_parts[0]
                filename = os.path.splitext(path_parts[1])[0]
                
                # 根据当前数据集类型确定特征存储位置
                if "occ" in self.dataset_name.lower():
                    feature_dir = os.path.join(self.feature_root, "occlusionLINEMOD", obj_name)
                else:
                    feature_dir = os.path.join(self.feature_root, "LINEMOD", obj_name)
            else:
                return None, None
            
            dino_path = os.path.join(feature_dir, f"{filename}_dino.pt")
            sam_path = os.path.join(feature_dir, f"{filename}_sam.pt")
            return dino_path, sam_path
    
    dataset = TestDataset()
    
    test_paths = [
        "LINEMOD/ape/rgb/000000.png",
        "templatesLINEMOD/train/ape/000001.png", 
        "templatesLINEMOD/test/ape/000002.png",
        "occlusionLINEMOD/ape/rgb/000003.png",
        "ape/000004.png"  # 简化格式 (LINEMOD)
    ]
    
    for path in test_paths:
        dino_path, sam_path = dataset._construct_feature_paths(path)
        print(f"  {path}")
        print(f"    -> DINO: {dino_path}")
        print(f"    -> SAM:  {sam_path}")
    
    # 测试occlusionLINEMOD的简化格式
    print("\n  测试occlusionLINEMOD简化格式:")
    occ_dataset = TestDataset("occlusionLINEMOD")
    dino_path, sam_path = occ_dataset._construct_feature_paths("ape/000005.png")
    print(f"  ape/000005.png (occ)")
    print(f"    -> DINO: {dino_path}")
    print(f"    -> SAM:  {sam_path}")

if __name__ == "__main__":
    test_feature_path_construction()
    test_offline_features() 