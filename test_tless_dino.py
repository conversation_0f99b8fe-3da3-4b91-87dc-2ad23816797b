import argparse
import os
import torch
from tqdm import tqdm
import numpy as np
from tabulate import tabulate
import time
from functools import partial
import multiprocessing
from torchvision import transforms

# 设置pyrender使用osmesa
os.environ["PYOPENGL_PLATFORM"] = "osmesa"

from lib.utils import weights, metrics, logger
from lib.utils.config import Config
from lib.datasets.dataloader_utils import init_dataloader

from lib.models.dino_network import DINOv2Extractor
from lib.models.dinov2.aggregation_network import AggregationNetwork
from lib.models.dino_feature_network import DINOv2FeatureExtractor

from lib.datasets.tless.dataloader_dino import TlessDino, TemplatesTlessDino
from lib.datasets.im_transform import im_transform, tensor2im
from lib.datasets.tless import inout
from lib.datasets.tless import testing_utils_dino as testing_utils
from lib.datasets.tless.eval_utils import eval_vsd
import os
os.environ['CUDA_VISIBLE_DEVICES'] = '0'

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--config_path', type=str)
    parser.add_argument('--checkpoint', type=str, required=True)
    parser.add_argument('--result_path', type=str, default='./dataset/results/vis/tless')
    args = parser.parse_args()

    config_global = Config(config_file="./config.json").get_config()
    config_run = Config(args.config_path).get_config()

    args.result_path = os.path.join(args.result_path, 'test')
    if not os.path.exists(args.result_path):
        os.makedirs(args.result_path)

    # initialize global config for testing
    dir_name = (args.config_path.split('/')[-1]).split('.')[0]
    print("config", dir_name)
    save_path = os.path.join(config_global.root_path, config_run.log.weights, dir_name)
    trainer_dir = os.getcwd()
    trainer_logger = logger.init_logger(save_path=save_path,
                                      trainer_dir=trainer_dir,
                                      trainer_logger_name=dir_name)

    # initialize model
    print("Initializing model...")
    dino_extractor = DINOv2Extractor(
        device="cuda",
        output_resolution=config_run.model.output_resolution,
        pose_tokens_config=config_run.model.pose_tokens,
        feature_blocks_config=config_run.model.feature_blocks,
        version=config_run.model.version
    )

    # 获取特征维度
    feature_dim = dino_extractor.feature_dim
    num_blocks = len(config_run.model.feature_blocks['indices'])
    feature_dims = [feature_dim] * num_blocks

    # 初始化聚合网络
    aggregation_network = AggregationNetwork(
        descriptor_size=config_run.model.descriptor_size,
        feature_dims=feature_dims,
        device="cuda",
        input_dim=feature_dim,
        use_pose_tokens=config_run.model.pose_tokens.get('use_attention', False),
        use_residual=config_run.model.get('use_residual', True)
    )
    model = DINOv2FeatureExtractor(
        config=config_run,
        threshold=0.2,
        dino_extractor=dino_extractor,
        aggregation_network=aggregation_network,
    ).cuda()

    # load checkpoint
    print("Loading checkpoint from:", args.checkpoint)
    checkpoint = torch.load(args.checkpoint)
    if 'model' in checkpoint:
        model.aggregation_network.load_state_dict(checkpoint['model'])
    if 'learnable_tokens' in checkpoint and model.dino_extractor.use_pose_tokens:
        model.dino_extractor.learnable_tokens = checkpoint['learnable_tokens']
    model.eval()

    # initialize transform
    print("Initializing transform...")
    transforms_list = [transforms.ToTensor()]
    if config_run.dataset.get('use_normalize', True):
        transforms_list.append(
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        )
    im_transform = transforms.Compose(transforms_list)

    # create dataloaders
    print("Creating dataloaders...")
    # 分为seen objects (1-18)和unseen objects (19-30)
    seen_id_obj = list(range(1, 7))
    unseen_id_obj = list(range(19, 21))

    config_loader = [
        ["seen_test", "test", "TLESS", seen_id_obj],
        ["unseen_test", "test", "TLESS", unseen_id_obj],
        ["seen_template", "test", "templatesTLESS", seen_id_obj],
        ["unseen_template", "test", "templatesTLESS", unseen_id_obj]
    ]

    datasetLoader = {}
    for config in config_loader:
        print("Dataset", config[0], config[2], config[3])
        if config[2] == "templatesTLESS":
            save_sample_path = os.path.join(config_global.root_path, config_run.dataset.sample_path, dir_name, config[0])
            loader = TemplatesTlessDino(
                root_dir=config_global.root_path,
                id_obj=config[3],  # 传入完整的物体ID列表
                image_size=config_run.dataset.image_size,
                im_transform=im_transform,
                save_path=save_sample_path
            )
        else:  # TLESS
            save_sample_path = os.path.join(config_global.root_path, config_run.dataset.sample_path, dir_name, config[0])
            loader = TlessDino(
                root_dir=config_global.root_path,
                dataset=config[2],
                list_id_obj=config[3],
                split=config[1],
                image_size=config_run.dataset.image_size,
                save_path=save_sample_path,
                use_normalize=config_run.dataset.get('use_normalize', True),
                use_mask=config_run.dataset.get('use_mask', False),
                gt_bbox_known=config_run.dataset.get('gt_bbox_known', True)
            )
        datasetLoader[config[0]] = loader
        print("---" * 20)

    datasetLoader = init_dataloader(dict_dataloader=datasetLoader,
                                  batch_size=config_run.train.batch_size,
                                  num_workers=config_run.train.num_workers)

    # testing
    print("Starting testing...")
    new_score = {}
    for split in ["seen_test"]:
        print(f"\nTesting {split}...")
        query_data = datasetLoader[split]
        template_data = datasetLoader[split.replace("test", "template")]
        
        # 执行测试
        list_err, list_pose_acc, list_class_acc, list_class_and_pose_acc15 = testing_utils.test(
            query_data=query_data,
            template_data=template_data,
            model=model,
            epoch=0,
            logger=None,
            save_prediction_path='./',
            gt_bbox_known=config_run.dataset.get('gt_bbox_known', True)
        )
        
        # 打印结果
        print(f"\n{split.upper()} Results:")
        print("=" * 50)
        
        # 准备表格数据
        metrics = ["error", "accuracy", "recognition", "recognition and pose"]
        headers = ["Object"] + metrics
        table_data = []
        
        # 添加mean行
        mean_row = ["Mean"]
        mean_row.append(f"{list_err['mean'].item():.4f}")
        mean_row.append(f"{list_pose_acc['mean'].item():.4f}")
        mean_row.append(f"{list_class_acc['mean'].item():.4f}")
        mean_row.append(f"{list_class_and_pose_acc15['mean'].item():.4f}")
        table_data.append(mean_row)
        
        # 添加每个物体的行
        # 获取当前评估结果中的所有对象ID（除了'mean'）
        obj_ids = sorted([k for k in list_err.keys() if isinstance(k, int)])
        for obj_id in obj_ids:
            row = [f"Object {obj_id:02d}"]
            row.append(f"{list_err[obj_id].item():.4f}")
            row.append(f"{list_pose_acc[obj_id].item():.4f}")
            row.append(f"{list_class_acc[obj_id].item():.4f}")
            row.append(f"{list_class_and_pose_acc15[obj_id].item():.4f}")
            table_data.append(row)
        
        # 打印表格
        print(tabulate(table_data, headers=headers, tablefmt="grid", floatfmt=".4f"))
        print("=" * 50)

if __name__ == "__main__":
    main() 