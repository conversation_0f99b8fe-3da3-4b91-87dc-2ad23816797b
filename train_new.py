import argparse
import os
import torch
import datetime
import wandb
import torch.nn as nn
import logging
import numpy as np
import random

from lib.utils import weights, metrics, logger
from lib.utils.optimizer import adjust_learning_rate
from lib.datasets.dataloader_utils import init_dataloader
from lib.utils.config import Config

# 导入新的网络结构
from lib.models.new_dino_network import NewDINOExtractor

from lib.datasets.linemod.dataloader_dino import LINEMODQuery, TemplatesLINEMOD
from lib.datasets.im_transform import im_transform, tensor2im
from lib.datasets.linemod import inout
from lib.datasets.linemod import training_utils_dino, testing_utils_dino
from tqdm import tqdm
import shutup
shutup.please()

# 设置CUDA设备
os.environ['CUDA_VISIBLE_DEVICES'] = '0'

# 固定随机种子，确保实验可重复
def set_random_seed(seed=42):
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    random.seed(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    print(f"🎲 Random seed fixed to {seed}")

def main():
    # 首先固定随机种子
    set_random_seed(42)

    parser = argparse.ArgumentParser()
    parser.add_argument('--config_path', type=str, default='config_run/LM_DINO_split1.json')
    parser.add_argument('--use_wandb', action='store_true')
    parser.add_argument('--wandb_name', type=str, help='指定运行的名称，如果不指定则使用默认格式：配置文件名_时间戳')
    parser.add_argument('--checkpoint', type=str, help='加载预训练模型的路径')
    parser.add_argument('--result_path', type=str, default='./dataset/results/vis/linemod')
    args = parser.parse_args()

    dir_name = (args.config_path.split('/')[-1]).split('.')[0]
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    run_name = f"new_{dir_name}_{timestamp}"
    print("config", run_name)

    # 生成运行名称
    if args.wandb_name:
        wandb_name = args.wandb_name
    else:
        wandb_name = f"new_{dir_name}_{timestamp}"

    # 设置日志目录
    log_dir = os.path.join('logs', run_name)
    os.makedirs(log_dir, exist_ok=True)

    # 加载配置
    config_global = Config(config_file="./config.json").get_config()
    config_run = Config(args.config_path).get_config()

    # 初始化日志
    save_path = os.path.join(config_global.root_path, config_run.log.weights, run_name)
    trainer_dir = os.path.join(os.getcwd(), "logs")
    trainer_logger = logger.init_logger(save_path=save_path,
                                    trainer_dir=trainer_dir,
                                    trainer_logger_name=run_name)

    trainer_logger.info(f"使用智能特征提取器自动选择")

    # 初始化模型
    dino_extractor = NewDINOExtractor(
        device="cuda",
        feature_blocks_config=config_run.model.feature_blocks,
        version=config_run.model.version,
        input_size=config_run.model.input_size
    )

    # 使用智能工厂函数自动选择合适的特征提取器
    from lib.models.simple_dino_feature_network import create_feature_extractor, OffsetPredictor
    from lib.models.learnable_hash import HashEncoder
    model = create_feature_extractor(
        config=config_run,
        dino_extractor=dino_extractor,
    ).cuda()

    # 检查是否启用偏移量预测，如果是则创建独立的偏移量预测器
    offset_predictor = None
    offset_enabled = getattr(config_run.model, 'offset_predictor', {}).get('enabled', False)
    if offset_enabled:
        print("🔧 Creating offset predictor for training...")
        offset_predictor = OffsetPredictor(config_run)

    # 检查是否启用Hash功能，如果是则创建独立的Hash编码器
    hash_encoder = None
    hash_enabled = getattr(config_run.model, 'learnable_hash', {}).get('enabled', False)
    hash_start_epoch = config_run.model.learnable_hash.get('start_epoch', 10) if hash_enabled else 0

    if hash_enabled:
        print("🔧 Creating hash encoder for training...")
        print(f"   📅 Hash training will start from epoch {hash_start_epoch}")
        hash_encoder = HashEncoder(config_run)

    if args.checkpoint is not None:
        print("Loading checkpoint from:", args.checkpoint)
        checkpoint = torch.load(args.checkpoint)
        if 'model' in checkpoint:
            model.load_state_dict(checkpoint['model'])
            # 如果有offset predictor，也加载其权重
            if offset_predictor is not None:
                offset_predictor.load_state_dict(checkpoint['model'])
            # 如果有hash encoder，也加载其权重
            if hash_encoder is not None:
                hash_encoder.load_state_dict(checkpoint['model'])
        elif 'aggregation_network' in checkpoint:
            # 兼容原始版本的checkpoint格式
            model.aggregation_network.load_state_dict(checkpoint['aggregation_network'])
        else:
            # 尝试直接加载整个checkpoint
            model.load_state_dict(checkpoint)
            # 如果有offset predictor，也加载其权重
            if offset_predictor is not None:
                offset_predictor.load_state_dict(checkpoint)
            # 如果有hash encoder，也加载其权重
            if hash_encoder is not None:
                hash_encoder.load_state_dict(checkpoint)

    # 加载数据
    transforms = im_transform()
    seen_id_obj, seen_names, seen_occ_id_obj, seen_occ_names, unseen_id_obj, unseen_names, \
        unseen_occ_id_obj, unseen_occ_names = inout.get_list_id_obj_from_split_name(config_run.dataset.split)

    # 定义数据加载器配置 - 添加LMO数据集
    config_loader = [["train", "train", "LINEMOD", seen_id_obj],
                    ["seen_test", "seen_test", "LINEMOD", seen_id_obj],
                    ["seen_template", "test", "templatesLINEMOD", seen_id_obj],
                    ["unseen_test", "test", "LINEMOD", unseen_id_obj],
                    ["unseen_template", "test", "templatesLINEMOD", unseen_id_obj],
                    ["seen_occ_test", "test", "occlusionLINEMOD", seen_occ_id_obj],
                    ["unseen_occ_test", "test", "occlusionLINEMOD", unseen_occ_id_obj],
                    ["seen_occ_template", "test", "templatesLINEMOD", seen_occ_id_obj],
                    ["unseen_occ_template", "test", "templatesLINEMOD", unseen_occ_id_obj]]

    # 检查模型的特征需求，动态配置数据加载器
    sam_enabled = config_run.model.efficient_sam.get('enabled', False)
    
    # 创建所有数据集
    datasetLoader = {}
    for config in config_loader:
        print("Dataset", config[0], config[2], config[3])

        # 🔧 新功能：测试数据集强制启用mask加载，训练数据集按配置
        is_test_dataset = config[0] != "train"
        use_mask_pooling_for_dataset = False if is_test_dataset else config_run.dataset.get('use_mask_pooling', False)

        if is_test_dataset:
            print(f"   📋 测试数据集 {config[0]} 强制启用mask加载用于精确相似度计算")

        if config[2] == "templatesLINEMOD":
            save_sample_path = os.path.join(config_global.root_path, config_run.dataset.sample_path, dir_name, config[0])
            dataset = TemplatesLINEMOD(
                root_dir=config_global.root_path,
                dataset=config[2],
                list_id_obj=config[3],
                split=config[1],
                image_size=config_run.dataset.image_size,
                save_path=save_sample_path,
                use_normalize=config_run.dataset.get('use_normalize', True),
                use_mask_pooling=use_mask_pooling_for_dataset
            )
        else:  # LINEMOD or occlusionLINEMOD
            save_sample_path = os.path.join(config_global.root_path, config_run.dataset.sample_path, dir_name, config[0])
            dataset = LINEMODQuery(
                root_dir=config_global.root_path,
                dataset=config[2],
                list_id_obj=config[3],
                split=config[1],
                image_size=config_run.dataset.image_size,
                save_path=save_sample_path,
                use_normalize=config_run.dataset.get('use_normalize', True),
                use_mask=config_run.dataset.get('use_mask', False),
                gt_bbox_known=config_run.dataset.get('gt_bbox_known', True),
                use_aug=config_run.dataset.get('use_aug', False),
                aug_config=config_run.dataset.get('aug', None),
                num_negatives=config_run.dataset.get('num_negatives', 1),
                use_mask_pooling=use_mask_pooling_for_dataset
            )
        datasetLoader[config[0]] = dataset
        print("---" * 20)

    # 使用init_dataloader初始化所有数据加载器
    datasetLoader = init_dataloader(
        dict_dataloader=datasetLoader,
        batch_size=config_run.train.batch_size,
        num_workers=config_run.train.num_workers
    )

    # 初始化优化器 - 只包含主要模块的参数（不包含Hash参数）
    # 重要：只包含requires_grad=True的参数
    optimizer_params = [p for p in model.parameters() if p.requires_grad]
    if offset_predictor is not None:
        optimizer_params.extend([p for p in offset_predictor.offset_predictor.parameters() if p.requires_grad])
        print("✅ Offset predictor参数已加入优化器")

    # 注意：Hash encoder参数不在初始优化器中，将在Hash训练阶段重新创建优化器
    if hash_encoder is not None:
        print("ℹ️  Hash encoder已创建，但参数将在Hash训练阶段才加入优化器")

    optimizer = torch.optim.Adam(
        optimizer_params,
        lr=config_run.train.optimizer.lr,
        weight_decay=config_run.train.optimizer.weight_decay)
    scores = metrics.init_score()

    # 初始化wandb
    if args.use_wandb:
        # 检查偏移量预测配置
        offset_enabled = getattr(config_run.model, 'offset_predictor', {}).get('enabled', False)

        wandb.init(
            project="pose-estimation-730",
            name=wandb_name,
            config={
                "learning_rate": config_run.train.optimizer.lr,
                "batch_size": config_run.train.batch_size,
                "epochs": config_run.train.epochs,
                "weight_decay": config_run.train.optimizer.weight_decay,
                "descriptor_size": config_run.model.descriptor_size,
                "split": config_run.dataset.split,
                "feature_blocks": config_run.model.feature_blocks.get('indices', []),
                "offset_prediction_enabled": offset_enabled,
                "offset_loss_weight": getattr(config_run.model, 'offset_predictor', {}).get('loss_weight', 0.1) if offset_enabled else 0
            }
        )

    # 在训练开始前打印模型信息
    model_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"特征提取器可训练参数数量: {model_params / 1e6:.2f}M")

    initial_optimizer_params = model_params
    if offset_predictor is not None:
        offset_params = sum(p.numel() for p in offset_predictor.offset_predictor.parameters() if p.requires_grad)
        initial_optimizer_params += offset_params
        print(f"偏移量预测器可训练参数数量: {offset_params / 1e6:.2f}M")
        print("✅ 偏移量预测功能已启用")
    else:
        print("❌ 偏移量预测功能未启用")

    if hash_encoder is not None:
        hash_params = (sum(p.numel() for p in hash_encoder.pose_hash_encoder.parameters() if p.requires_grad) +
                      sum(p.numel() for p in hash_encoder.cls_hash_encoder.parameters() if p.requires_grad))
        print(f"Hash编码器可训练参数数量: {hash_params / 1e6:.2f}M")
        print("✅ Hash编码功能已启用")
        print(f"   📅 Hash训练将从epoch {hash_start_epoch}开始")
        print(f"   📊 Hash阶段优化器参数数量: {hash_params / 1e6:.2f}M")
    else:
        print("❌ Hash编码功能未启用")

    print(f"初始阶段优化器参数数量: {initial_optimizer_params / 1e6:.2f}M")

    # 训练循环
    for epoch in tqdm(range(0, config_run.train.epochs)):
        # 判断当前训练阶段，动态决定传递哪些模块
        is_hash_stage = hash_enabled and epoch >= hash_start_epoch

        # 实验1：简化的Hash训练，等价于wrap版本
        if is_hash_stage and epoch == hash_start_epoch:
            print(f"\n🔄 Epoch {epoch}: 切换到Hash训练阶段（实验1：简化版本）")
            print("   🎯 目标：复现wrap版本的训练方式")
            print("   📝 配置：只使用Hash特征损失 + Hash正则化损失")

            # 🔧 简化版优化器：所有参数使用统一学习率，类似wrap版本
            all_optimizer_params = []

            # 添加主要模型参数
            all_optimizer_params.extend([p for p in model.parameters() if p.requires_grad])

            # 添加offset predictor参数（如果启用）
            if offset_predictor is not None:
                all_optimizer_params.extend([p for p in offset_predictor.offset_predictor.parameters() if p.requires_grad])

            # 添加Hash编码器参数
            if hash_encoder is not None:
                all_optimizer_params.extend(list(hash_encoder.pose_hash_encoder.parameters()))
                all_optimizer_params.extend(list(hash_encoder.cls_hash_encoder.parameters()))

            # 简化的优化器：统一学习率，类似wrap版本
            optimizer = torch.optim.Adam(
                all_optimizer_params,
                lr=config_run.train.optimizer.lr,  # 统一学习率
                weight_decay=config_run.train.optimizer.weight_decay
            )

            # 统计参数数量
            main_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
            offset_params = sum(p.numel() for p in offset_predictor.offset_predictor.parameters() if p.requires_grad) if offset_predictor else 0
            hash_params = sum(p.numel() for p in hash_encoder.pose_hash_encoder.parameters()) + sum(p.numel() for p in hash_encoder.cls_hash_encoder.parameters()) if hash_encoder else 0
            total_params = main_params + offset_params + hash_params

            print(f"   📊 简化训练参数统计:")
            print(f"      - 主要模型: {main_params / 1e6:.2f}M")
            if offset_predictor:
                print(f"      - 偏移预测器: {offset_params / 1e6:.2f}M")
            if hash_encoder:
                print(f"      - Hash编码器: {hash_params / 1e6:.2f}M")
            print(f"      - 总计: {total_params / 1e6:.2f}M")
            print(f"   📈 统一学习率: {config_run.train.optimizer.lr}")
            print("   ✅ 优化器已切换到简化Hash训练模式（等价wrap版本）")

        # 🔧 简化的学习率调度
        if epoch in config_run.train.scheduler.milestones:
            # 简化版：统一应用学习率衰减
            for param_group in optimizer.param_groups:
                param_group['lr'] *= config_run.train.scheduler.gamma
            print(f"   📉 学习率已衰减，当前学习率: {optimizer.param_groups[0]['lr']}")

        # 训练一个epoch
        train_loss = training_utils_dino.train(
            train_data=datasetLoader["train"],
            model=model,
            optimizer=optimizer,
            warm_up_config=[1000, config_run.train.optimizer.lr],
            epoch=epoch,
            logger=trainer_logger,
            log_interval=config_run.log.log_interval,
            regress_delta=config_run.model.regression_loss,
            use_wandb=args.use_wandb,
            config_run=config_run,
            offset_predictor=offset_predictor,                    # 直接传递
            hash_encoder=hash_encoder if is_hash_stage else None  # 只有hash encoder需要动态传递
        )

        # 每1个epoch进行测试
        if (epoch + 1) % 1 == 0:
            trainer_logger.info(f"Testing at epoch {epoch + 1}")

            # 确定测试模式：根据配置决定是否需要测试两种模式
            config_gt_bbox_known = config_run.dataset.gt_bbox_known
            if config_gt_bbox_known:
                # 配置为True：只测试gt_bbox_known=True模式
                test_modes = [("known", True)]
                print("📋 测试模式：仅测试 gt_bbox_known=True (已知边界框)")
            else:
                # 配置为False：测试两种模式
                test_modes = [("known", True), ("unknown", False)]
                print("📋 测试模式：测试 gt_bbox_known=True 和 gt_bbox_known=False (已知+未知边界框)")

            # 进行测试 - 测试所有4个数据集：seen、unseen、seen_occ、unseen_occ
            for config_split in [["seen", seen_id_obj], ["unseen", unseen_id_obj], ["seen_occ", seen_occ_id_obj], ["unseen_occ", unseen_occ_id_obj]]:
                query_name = config_split[0] + "_test"
                template_name = config_split[0] + "_template"

                # 对每个数据集，根据test_modes进行测试
                for mode_name, gt_bbox_known in test_modes:
                    print(f"\n🔍 Testing {config_split[0]} - {mode_name} mode (gt_bbox_known={gt_bbox_known})...")

                    # 为不同模式创建独立的结果保存路径
                    mode_result_path = os.path.join(args.result_path, mode_name)
                    os.makedirs(mode_result_path, exist_ok=True)

                    # 确保模型和Hash编码器在eval模式，并清理训练状态
                    model.eval()
                    if hash_encoder is not None and is_hash_stage:
                        hash_encoder.pose_hash_encoder.eval()
                        hash_encoder.cls_hash_encoder.eval()

                    # 清理梯度缓存，确保测试状态干净
                    torch.cuda.empty_cache()
                    with torch.no_grad():  # 确保测试时不计算梯度
                        testing_score = testing_utils_dino.test(
                            query_data=datasetLoader[query_name],
                            template_data=datasetLoader[template_name],
                            model=model,
                            split_name=config_split[0],
                            list_id_obj=config_split[1].tolist(),
                            epoch=epoch,
                            logger=trainer_logger,
                            vis=False,
                            result_vis_path=mode_result_path,  # 使用模式特定的路径
                            tensor2im=tensor2im,
                            gt_bbox_known=gt_bbox_known,  # 使用当前测试模式的设置
                            offset_predictor=offset_predictor,                    # 直接传递
                            hash_encoder=hash_encoder if is_hash_stage else None  # 只有hash encoder需要动态传递
                        )

                    # 测试完成后，将模型设回训练模式
                    model.train()
                    if hash_encoder is not None and is_hash_stage:
                        hash_encoder.pose_hash_encoder.train()
                        hash_encoder.cls_hash_encoder.train()

                    # 构建带模式标识的指标名称
                    mode_suffix = f"_{mode_name}" if len(test_modes) > 1 else ""

                    # 记录测试结果到日志
                    trainer_logger.info(f"Epoch {epoch + 1} - {config_split[0]}{mode_suffix} test error: {testing_score['error, mean']:.4f}")
                    trainer_logger.info(f"Epoch {epoch + 1} - {config_split[0]}{mode_suffix} test accuracy5: {testing_score.get('accuracy5, mean', 0):.4f}")
                    trainer_logger.info(f"Epoch {epoch + 1} - {config_split[0]}{mode_suffix} test accuracy10: {testing_score.get('accuracy10, mean', 0):.4f}")
                    trainer_logger.info(f"Epoch {epoch + 1} - {config_split[0]}{mode_suffix} test accuracy15: {testing_score.get('accuracy15, mean', 0):.4f}")

                    # 记录测试结果到wandb - 使用分组结构
                    if args.use_wandb:
                        # 为不同模式创建分组
                        if len(test_modes) > 1:
                            # 双模式：使用分组结构
                            wandb_log_dict = {
                                f"{mode_name}/{config_split[0]}_test_error": testing_score['error, mean'],
                                f"{mode_name}/{config_split[0]}_test_accuracy5": testing_score.get('accuracy5, mean', 0),
                                f"{mode_name}/{config_split[0]}_test_accuracy10": testing_score.get('accuracy10, mean', 0),
                                f"{mode_name}/{config_split[0]}_test_accuracy15": testing_score.get('accuracy15, mean', 0),
                                f"{mode_name}/{config_split[0]}_test_recognition": testing_score.get('recognition, mean', 0),
                                f"{mode_name}/{config_split[0]}_test_recognition_and_pose5": testing_score.get('recognition and pose5, mean', 0),
                                f"{mode_name}/{config_split[0]}_test_recognition_and_pose10": testing_score.get('recognition and pose10, mean', 0),
                                f"{mode_name}/{config_split[0]}_test_recognition_and_pose15": testing_score.get('recognition and pose15, mean', 0),
                                "epoch": epoch + 1
                            }
                        else:
                            # 单模式：保持原有结构
                            wandb_log_dict = {
                                f"{config_split[0]}_test_error": testing_score['error, mean'],
                                f"{config_split[0]}_test_accuracy5": testing_score.get('accuracy5, mean', 0),
                                f"{config_split[0]}_test_accuracy10": testing_score.get('accuracy10, mean', 0),
                                f"{config_split[0]}_test_accuracy15": testing_score.get('accuracy15, mean', 0),
                                f"{config_split[0]}_test_recognition": testing_score.get('recognition, mean', 0),
                                f"{config_split[0]}_test_recognition_and_pose5": testing_score.get('recognition and pose5, mean', 0),
                                f"{config_split[0]}_test_recognition_and_pose10": testing_score.get('recognition and pose10, mean', 0),
                                f"{config_split[0]}_test_recognition_and_pose15": testing_score.get('recognition and pose15, mean', 0),
                                "epoch": epoch + 1
                            }
                        wandb.log(wandb_log_dict)

            # 保存模型
            save_file = os.path.join(save_path, 'model_epoch{}.pth'.format(epoch))
            trainer_logger.info("Saving to {}".format(save_file))
            os.makedirs(os.path.dirname(save_file), exist_ok=True)
            
            # 准备保存的状态字典 - 兼容不同模型类型
            if hasattr(model, 'aggregation_network'):
                # 保存聚合网络的状态（兼容原始版本）
                state_dict = {
                    'model': model.state_dict(),
                    'aggregation_network': model.aggregation_network.state_dict()
                }
            else:
                # 简化版本直接保存整个模型
                state_dict = {
                    'model': model.state_dict()
                }

            # 如果有offset_predictor，也保存其权重
            if offset_predictor is not None:
                # 将offset_predictor的权重添加到model的state_dict中
                model_state = state_dict['model']
                for key, value in offset_predictor.offset_predictor.state_dict().items():
                    model_state[f'offset_predictor.{key}'] = value
                state_dict['model'] = model_state
                trainer_logger.info("Offset predictor weights included in checkpoint")

            # 如果有hash_encoder，也保存其权重
            if hash_encoder is not None:
                model_state = state_dict['model']
                # 保存pose hash encoder权重
                for key, value in hash_encoder.pose_hash_encoder.state_dict().items():
                    model_state[f'pose_hash_encoder.{key}'] = value
                # 保存cls hash encoder权重
                for key, value in hash_encoder.cls_hash_encoder.state_dict().items():
                    model_state[f'cls_hash_encoder.{key}'] = value
                state_dict['model'] = model_state
                trainer_logger.info("Hash encoder weights included in checkpoint")

            torch.save(state_dict, save_file)
        else:
            # 非测试epoch只记录训练损失
            trainer_logger.info(f'Epoch-{epoch}: train_loss={train_loss}')


if __name__ == "__main__":
    main() 