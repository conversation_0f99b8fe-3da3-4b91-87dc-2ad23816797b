import argparse
import os
import torch
from tqdm import tqdm
import numpy as np

from lib.utils import weights, metrics, logger
from lib.utils.config import Config
from lib.datasets.dataloader_utils import init_dataloader

from lib.models.dino_network import DINOv2Extractor
from lib.models.dinov2.aggregation_network import AggregationNetwork
from lib.models.dino_feature_network import DINOv2FeatureExtractor

from lib.datasets.bop.dataloader_query import BOPDataset
from lib.datasets.bop.dataloader_template import TemplatesBOPDataset
from lib.datasets.im_transform import im_transform as get_transform
from lib.datasets.linemod import testing_utils_dino as testing_utils
from lib.datasets.linemod import inout

import os 
os.environ['CUDA_VISIBLE_DEVICES'] = '2'

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--split', type=str, choices=['split1', 'split2', 'split3'])
    parser.add_argument('--config_path', type=str)
    parser.add_argument('--checkpoint', type=str, required=True)
    parser.add_argument('--sam_crops_path', type=str, 
                       default="/home/<USER>/projects/RJL2025/my-diff-feats-pose/dataset/sam_crops",
                       help="Path to sam_crops directory")
    parser.add_argument('--dataset', type=str, choices=['lm', 'lmo'],
                       default="lm", help="Dataset name (lm or lmo)")
    args = parser.parse_args()

    config_global = Config(config_file="./config.json").get_config()
    config_run = Config(args.config_path).get_config()

    # initialize global config for testing
    dir_name = (args.config_path.split('/')[-1]).split('.')[0]
    print("config", dir_name)
    save_path = os.path.join(config_global.root_path, config_run.log.weights, dir_name)
    trainer_dir = os.path.join(os.getcwd(), "logs")
    trainer_logger = logger.init_logger(save_path=save_path,
                                      trainer_dir=trainer_dir,
                                      trainer_logger_name=dir_name)

    # initialize model
    print("Initializing model...")
    dino_extractor = DINOv2Extractor(
        device="cuda",
        output_resolution=config_run.model.output_resolution,
        pose_tokens_config=config_run.model.pose_tokens,
        feature_blocks_config=config_run.model.feature_blocks,
        version=config_run.model.version
    )

    # 获取特征维度
    feature_dim = dino_extractor.feature_dim
    num_blocks = len(config_run.model.feature_blocks['indices'])
    feature_dims = [feature_dim] * num_blocks

    # 初始化聚合网络
    aggregation_network = AggregationNetwork(
        descriptor_size=config_run.model.descriptor_size,
        feature_dims=feature_dims,
        device="cuda",
        input_dim=feature_dim,
        use_pose_tokens=config_run.model.pose_tokens.get('use_attention', False)
    )
    model = DINOv2FeatureExtractor(
        config=config_run,
        threshold=0.2,
        dino_extractor=dino_extractor,
        aggregation_network=aggregation_network,
    ).cuda()

    # load checkpoint
    print("Loading checkpoint from:", args.checkpoint)
    checkpoint = torch.load(args.checkpoint)
    if 'model' in checkpoint:
        model.aggregation_network.load_state_dict(checkpoint['model'])
    if 'learnable_tokens' in checkpoint and model.dino_extractor.use_pose_tokens:
        model.dino_extractor.learnable_tokens = checkpoint['learnable_tokens']
    model.eval()

    # initialize transform
    print("Initializing transform...")
    transform = get_transform()

    # create dataloaders
    print("Creating dataloaders...")
    
    # 从split名称获取对象ID列表
    seen_id_obj, seen_names, seen_occ_id_obj, seen_occ_names, unseen_id_obj, unseen_names, \
        unseen_occ_id_obj, unseen_occ_names = inout.get_list_id_obj_from_split_name(config_run.dataset.split)
    
    # 配置数据集
    config_splits = {
        "seen": seen_id_obj,
        "unseen": unseen_id_obj,
        "seen_occ": seen_occ_id_obj,
        "unseen_occ": unseen_occ_id_obj
    }

    datasetLoader = {}
    for split_name, list_id_obj in config_splits.items():
        if list_id_obj is None or len(list_id_obj) == 0:
            continue
            
        # 查询数据集
        query_dataset = BOPDataset(
            root_dir=args.sam_crops_path,
            dataset=args.dataset,
            list_id_obj=list_id_obj,
            split='test',
            image_size=config_run.dataset.image_size,
            mask_size=config_run.dataset.mask_size,
            im_transform=transform,
            save_path=save_path
        )
        datasetLoader[f"{split_name}_test"] = query_dataset

        # 模板数据集
        template_dataset = TemplatesBOPDataset(
            root_dir=args.sam_crops_path,
            dataset=args.dataset,
            list_id_obj=list_id_obj,
            split='test',
            image_size=config_run.dataset.image_size,
            mask_size=config_run.dataset.mask_size,
            im_transform=transform,
            save_path=save_path
        )
        datasetLoader[f"{split_name}_template"] = template_dataset

    # testing
    print("Starting testing...")
    new_score = {}
    with torch.no_grad():
        for split_name, list_id_obj in config_splits.items():
            if list_id_obj is None or len(list_id_obj) == 0:
                continue
                
            query_name = f"{split_name}_test"
            template_name = f"{split_name}_template"
            
            print(f"\nTesting {split_name}...")
            err, acc, recog, class_and_pose = testing_utils.test(
                query_data=datasetLoader[query_name],
                template_data=datasetLoader[template_name],
                model=model,
                split_name=split_name,
                list_id_obj=list_id_obj,
                epoch=-1,
                logger=trainer_logger
            )
            
            # 保存分数
            new_score[f"{split_name}_err"] = err
            new_score[f"{split_name}_acc"] = acc
            new_score[f"{split_name}_recog"] = recog
            new_score[f"{split_name}_class_and_pose"] = class_and_pose

    # 打印最终分数
    print("\nFinal Scores:")
    print(new_score)

if __name__ == "__main__":
    main() 