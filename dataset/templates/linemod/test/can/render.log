Selecting render devices...
Device NVIDIA GeForce RTX 3090 of type OPTIX found and used.
Device NVIDIA GeForce RTX 3090 of type OPTIX found and used.
Device NVIDIA GeForce RTX 3090 of type OPTIX found and used.
Device NVIDIA GeForce RTX 3090 of type OPTIX found and used.
Device NVIDIA GeForce RTX 3090 of type OPTIX found and used.
Device NVIDIA GeForce RTX 3090 of type OPTIX found and used.
Device NVIDIA GeForce RTX 3090 of type OPTIX found and used.
Device Intel Xeon Gold 6238R CPU @ 2.20GHz of type CPU found and used.
Error: Python: Traceback (most recent call last):
  File "/data1/renjl/projects/diff-feats-pose/./lib/renderer/blenderproc.py", line 107, in <module>
    render_blender_proc(args.cad_path, args.output_dir, poses, intrinsic=intrinsic, img_size=img_size,
  File "/data1/renjl/projects/diff-feats-pose/./lib/renderer/blenderproc.py", line 39, in render_blender_proc
    obj = bproc.loader.load_obj(cad_path)[0]
  File "/data1/renjl/anaconda3/envs/diff-feats/lib/python3.8/site-packages/blenderproc/python/loader/ObjectLoader.py", line 31, in load_obj
    raise FileNotFoundError(f"The given filepath does not exist: {filepath}")
FileNotFoundError: The given filepath does not exist: ../dataset/linemod/models/models/obj_000005.ply
