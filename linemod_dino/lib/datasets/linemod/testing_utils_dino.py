import os, time
from tqdm import tqdm
import numpy as np
import torch
import torch.nn.functional as F

from lib.utils.metrics import AverageValueMeter
from lib.datasets.linemod import inout
from lib.datasets.linemod.visualization import visualize_result


def calculate_score(pred_location, gt_location, id_symmetry, id_obj, pred_id_obj):
    unique_ids, inverse_indices = torch.unique(id_obj, sorted=True, return_inverse=True)
    cosine_sim = F.cosine_similarity(pred_location, gt_location)
    angle_err = torch.rad2deg(torch.arccos(cosine_sim.clamp(min=-1, max=1)))

    # for symmetry
    gt_location_opposite = gt_location.clone()  # 使用clone避免修改原始数据
    gt_location_opposite[:, :2] *= -1  # rotation 180 in Z axis
    cosine_sim_sym = F.cosine_similarity(pred_location, gt_location_opposite)  # 修正：计算与预测位置的相似度
    angle_err_sym = torch.rad2deg(torch.arccos(cosine_sim_sym.clamp(min=-1, max=1)))
    angle_err[id_symmetry == 1] = torch.minimum(angle_err[id_symmetry == 1], angle_err_sym[id_symmetry == 1])

    list_err, list_pose_acc, list_class_acc, list_class_and_pose_acc15 = {}, {}, {}, {}
    for i in range(len(unique_ids)):
        err = angle_err[id_obj == unique_ids[i]]
        recognition_acc = (pred_id_obj[id_obj == unique_ids[i]] == unique_ids[i])
        
        class_and_pose_acc15 = torch.logical_and(err <= 15, recognition_acc).float().mean()
        err = err.mean()
        recognition_acc = recognition_acc.float().mean()
        pose_acc = (err <= 15).float().mean()

        list_err[unique_ids[i].item()] = err
        list_pose_acc[unique_ids[i].item()] = pose_acc
        list_class_acc[unique_ids[i].item()] = recognition_acc
        list_class_and_pose_acc15[unique_ids[i].item()] = class_and_pose_acc15

    list_err["mean"] = torch.mean(angle_err)
    list_pose_acc["mean"] = (angle_err <= 15).float().mean()
    list_class_acc["mean"] = (pred_id_obj == id_obj).float().mean()
    list_class_and_pose_acc15["mean"] = torch.logical_and(angle_err <= 15, pred_id_obj == id_obj).float().mean()

    return list_err, list_pose_acc, list_class_acc, list_class_and_pose_acc15


def calculate_similarity(query_features, template_features):
    """
    计算query和template之间的相似度矩阵。
    :param query_features: B*L的query特征
    :param template_features: N*L的template特征
    :return: B*N的相似度矩阵
    """
    # 计算余弦相似度
    query_norm = query_features / query_features.norm(dim=1, keepdim=True)
    template_norm = template_features / template_features.norm(dim=1, keepdim=True)
    similarity_matrix = torch.mm(query_norm, template_norm.t())
    return similarity_matrix


def test(query_data, template_data, model, epoch, logger, split_name, list_id_obj,
         result_vis_path=None, vis=False, tensor2im=None, gt_bbox_known=True):
    start_time = time.time()
    list_id_obj.append("mean")
    meter_error = {id_obj: AverageValueMeter() for id_obj in list_id_obj}
    meter_accuracy = {id_obj: AverageValueMeter() for id_obj in list_id_obj}
    meter_recognition = {id_obj: AverageValueMeter() for id_obj in list_id_obj}
    meter_accuracy_class_and_pose = {id_obj: AverageValueMeter() for id_obj in list_id_obj}
    
    query_size, query_dataloader = len(query_data), iter(query_data)
    template_size, template_dataloader = len(template_data), iter(template_data)
    
    monitoring_text = "Epoch-{}, {} -- Mean err: {:.2f}, Acc: {:.2f}, Rec : {:.2f}, Class and Pose  : {:.2f}"
    timing_text = "Validation time for epoch {}: {:.02f} minutes"

    model.eval()
    with torch.no_grad():
        # 处理模板数据
        list_cls_feature_template, list_pose_feature_template = [], []
        list_synthetic_pose, list_id_obj_template = [], []
        list_img_template = []

        for miniBatch in tqdm(template_dataloader, desc="处理模板数据"):
            template = miniBatch["template"].cuda()
            obj_pose = miniBatch["obj_pose"].cuda()
            id_obj = miniBatch["id_obj"].cuda()
            features = model(template)
            cls_feature = features['cls_feature']
            pose_feature = features['pose_feature']

            list_synthetic_pose.append(obj_pose)
            list_id_obj_template.append(id_obj)
            list_cls_feature_template.append(cls_feature)
            list_pose_feature_template.append(pose_feature)

            if vis:
                list_img_template.append(tensor2im(template))

        list_cls_feature_template = torch.cat(list_cls_feature_template, dim=0)
        list_pose_feature_template = torch.cat(list_pose_feature_template, dim=0)
        list_synthetic_pose = torch.cat(list_synthetic_pose, dim=0)
        list_id_obj_template = torch.cat(list_id_obj_template, dim=0)
        if vis:
            list_img_template = np.concatenate(list_img_template, axis=0)

        # 按照类别对特征进行分类
        cls_feature_dict = {}
        pose_feature_dict = {}
        synthetic_pose_dict = {}

        for i, id_obj in enumerate(list_id_obj_template):
            id_obj = id_obj.item()
            if id_obj not in cls_feature_dict:
                cls_feature_dict[id_obj] = []
                pose_feature_dict[id_obj] = []
                synthetic_pose_dict[id_obj] = []
            cls_feature_dict[id_obj].append(list_cls_feature_template[i])
            pose_feature_dict[id_obj].append(list_pose_feature_template[i])
            synthetic_pose_dict[id_obj].append(list_synthetic_pose[i])

        # 将每个类别的特征转换为张量
        for id_obj in cls_feature_dict.keys():
            cls_feature_dict[id_obj] = torch.stack(cls_feature_dict[id_obj])
            pose_feature_dict[id_obj] = torch.stack(pose_feature_dict[id_obj])
            synthetic_pose_dict[id_obj] = torch.stack(synthetic_pose_dict[id_obj])

        # 测试每个查询图像
        for miniBatch in tqdm(query_dataloader, desc="处理查询图像"):
            query = miniBatch["query"].cuda()
            obj_pose = miniBatch["obj_pose"].cuda()
            id_obj = miniBatch["id_obj"].cuda()
            id_symmetry = miniBatch["id_symmetry"].cuda()

            feature_query = model(query)

            if gt_bbox_known:
                # 根据类别索引找到对应类别的模板特征
                pose_feature_templates = pose_feature_dict[id_obj.item()]
                synthetic_poses = synthetic_pose_dict[id_obj.item()]
                # get best template
                sim_score_pose = calculate_similarity(
                    feature_query['pose_feature'], pose_feature_templates
                )
                weight_sim_pose, pred_index_pose = sim_score_pose.topk(k=1)
                pred_pose = synthetic_poses[pred_index_pose.reshape(-1)]
                pred_id_obj = id_obj  # 直接使用query的id_obj

                err, acc, class_score, class_and_pose = calculate_score(pred_location=pred_pose,
                                                                        gt_location=obj_pose,
                                                                        id_symmetry=id_symmetry,
                                                                        id_obj=id_obj,
                                                                        pred_id_obj=pred_id_obj)
            else:
                other_image = miniBatch['cls_negatives'][0].cuda()
                other_cls_features = model(other_image)['cls_feature']
                cur_cls_feature = feature_query['cls_feature']  # [1, C]
                # 拼接当前特征和其他图像特征
                combined_features = torch.cat([cur_cls_feature, other_cls_features], dim=0)  # [(B+1), C]
                # 计算与模板特征的相似度
                cur_template_pose_features = cls_feature_dict[id_obj.item()]  # [N, C]
                sim = F.cosine_similarity(combined_features.unsqueeze(1),
                                       cur_template_pose_features.unsqueeze(0),
                                       dim=2)  # [(B+1), N]

                # 找到最相似的配对
                max_sim_values, max_sim_indices = torch.max(sim, dim=1)  # [(B+1)]
                max_pair_idx = torch.argmax(max_sim_values)  # 标量

                # 判断是否预测正确（最相似的是否为原始特征）
                is_correct = (max_pair_idx == 0)  # 如果是0，说明原始特征是最相似的


                # 如果类别预测正确，执行姿态匹配
                if is_correct:
                    pose_feature_templates = pose_feature_dict[id_obj.item()]
                    synthetic_poses = synthetic_pose_dict[id_obj.item()]
                    # get best template
                    sim_score_pose = calculate_similarity(
                        feature_query['pose_feature'], pose_feature_templates
                    )
                    weight_sim_pose, pred_index_pose = sim_score_pose.topk(k=1)
                    pred_pose = synthetic_poses[pred_index_pose.reshape(-1)]
                    pred_id_obj = id_obj  # 直接使用query的id_obj
                    err, acc, class_score, class_and_pose = calculate_score(pred_location=pred_pose,
                                                                            gt_location=obj_pose,
                                                                            id_symmetry=id_symmetry,
                                                                            id_obj=id_obj,
                                                                            pred_id_obj=pred_id_obj)
                # 如果类别预测错误，直接视为类别预测错误，不再进行姿态匹配
                else:
                    class_score={id_obj.item():torch.tensor(0).to(id_obj.device),'mean':torch.tensor(0).to(id_obj.device)}
                    class_and_pose={id_obj.item():torch.tensor(0).to(id_obj.device),'mean':torch.tensor(0).to(id_obj.device)}
                    err, acc = None, None
                        

            for key in class_and_pose.keys():
                if err is not None:
                    meter_error[key].update(err[key].item())
                if acc is not None:
                    meter_accuracy[key].update(acc[key].item())
                meter_recognition[key].update(class_score[key].item())
                meter_accuracy_class_and_pose[key].update(class_and_pose[key].item())
        
                if vis:
                    pred_template = list_img_template[pred_index_pose.reshape(-1).cpu().numpy()]
                    sim_m_pose = sim_score_pose[pred_index_pose.reshape(-1)]

                    for q_img, t_img, sim, id, s, p in zip(
                            tensor2im(query),
                            pred_template,
                            sim_m_pose.cpu().numpy(),
                            id_obj.cpu().numpy(),
                            weight_sim_pose.cpu().numpy(),
                            obj_pose.cpu().numpy()
                    ):
                        visualize_result(
                            q_img, t_img, sim, id, s[0], p,
                            os.path.join(result_vis_path, split_name)
                        )

        scores = [meter_error, meter_accuracy, meter_recognition, meter_accuracy_class_and_pose]
        results = {}
        for idx_metric, metric_name in enumerate(["error", "accuracy", "recognition", "recognition and pose"]):
            for id_obj in list_id_obj:
                if id_obj == "mean":
                    obj_name = "mean"
                else:
                    obj_name = inout.LINEMOD_real_id_to_name[id_obj]
                key_name = "{}, {}".format(metric_name, obj_name)
                results[key_name] = scores[idx_metric][id_obj].avg
        filled_monitoring_text = monitoring_text.format(epoch, split_name,
                                                        meter_error["mean"].avg,
                                                        meter_accuracy["mean"].avg,
                                                        meter_recognition["mean"].avg,
                                                        meter_accuracy_class_and_pose["mean"].avg)
        logger.info(filled_monitoring_text)
        logger.info(timing_text.format(epoch, (time.time() - start_time) / 60))
    return results 