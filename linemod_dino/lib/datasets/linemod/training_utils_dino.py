import time
import os
from tqdm import tqdm
import torch
import torch.nn.functional as F
import wandb

from lib.utils.metrics import AverageValueMeter
from lib.losses.contrast_loss import GlobalLoss


def train(
        train_data, model, optimizer, warm_up_config, epoch, logger, log_interval, regress_delta, use_wandb=False, config_run=None
):
    start_time = time.time()
    # 位姿相似度度量
    meter_pose_pos_sim = AverageValueMeter()
    meter_pose_neg_sim = AverageValueMeter()
    meter_pose_loss = AverageValueMeter()
    # 类别相似度度量
    meter_cls_pos_sim = AverageValueMeter()
    meter_cls_neg_sim = AverageValueMeter()
    meter_cls_loss = AverageValueMeter()
    # 总loss度量
    meter_total_loss = AverageValueMeter()

    # 初始化GlobalLoss，使用较小的温度参数
    cls_loss_weight = config_run.model.get('cls_loss_weight', 0.5) if config_run else 0.5
    criterion = GlobalLoss(tau=0.1, margin=0.01, cls_loss_weight=cls_loss_weight).cuda()

    timing_text = "Training time for epoch {}: {:.02f} minutes"
    monitoring_text = 'Epoch-{} -- Iter [{}/{}] total_loss: {:.3f}, pose_loss: {:.3f}(pos:{:.3f},neg:{:.3f}), cls_loss: {:.3f}(pos:{:.3f},neg:{:.3f})'

    model.train()
    train_size = len(train_data)
    train_loader = iter(train_data)

    for i, miniBatch in enumerate(tqdm(train_loader)):
        # update learning rate with warm up
        if warm_up_config is not None:
            [nb_iter_warm_up, lr] = warm_up_config
            nb_iter = epoch * train_size + i
            if nb_iter <= nb_iter_warm_up:
                lrUpdate = nb_iter / float(nb_iter_warm_up) * lr
                for g in optimizer.param_groups:
                    g['lr'] = lrUpdate

        # 加载数据
        query = miniBatch["query"].cuda()  # [B, C, H, W]
        template = miniBatch["template"].cuda()  # [B, C, H, W]
        pose_negatives = torch.stack([neg.cuda() for neg in miniBatch["pose_negatives"]], dim=1)  # [B, num_negatives, C, H, W]
        cls_negative = miniBatch["cls_negative"].cuda()  # [B, C, H, W]
        pose_negative_deltas = miniBatch["pose_negative_deltas"].cuda().float()  # [B, num_negatives]
        
        # 提取特征
        query_features = model(query)
        template_features = model(template)
        
        # 顺序处理负样本
        B, num_negatives = pose_negatives.shape[:2]
        pose_negative_features_list = []
        for i in range(num_negatives):
            # 获取第i个负样本 [B, 3, 512, 512]
            neg = pose_negatives[:, i]
            neg_features = model(neg)  # 返回字典
            # 从字典中提取pose特征
            neg_pose_features = neg_features['pose_feature']  # [B, D]
            pose_negative_features_list.append(neg_pose_features)
        
        # 将所有负样本特征堆叠 [B, num_negatives, D]
        pose_negative_features = torch.stack(pose_negative_features_list, dim=1)
        
        # 计算与所有负样本的相似度
        # query_features['pose_feature']: [B, D]
        # pose_negative_features: [B, num_negatives, D]
        # 计算每个query与所有负样本的相似度
        pose_similarities_neg = []
        for i in range(B):
            # 计算第i个query与所有负样本的相似度
            query_feat = query_features['pose_feature'][i]  # [D]
            neg_feats = pose_negative_features[i]  # [num_negatives, D]
            similarities = F.cosine_similarity(
                query_feat.unsqueeze(0),  # [1, D]
                neg_feats,  # [num_negatives, D]
                dim=1  # 在特征维度上计算相似度
            )  # [num_negatives]
            pose_similarities_neg.append(similarities)
        pose_similarities_neg = torch.stack(pose_similarities_neg, dim=0)  # [B, num_negatives]
        
        cls_negative_features = model(cls_negative)

        # 计算位姿相似度（正样本）
        pose_similarity_matrix = model.calculate_similarity(
            query_features['pose_feature'],
            template_features['pose_feature'],
            feature_type='pose'
        )  # [B, B]
        pose_similarity_pos = pose_similarity_matrix.diag()  # [B]

        # 计算类别相似度
        cls_similarity_matrix = model.calculate_similarity(
            query_features['cls_feature'],
            template_features['cls_feature'],
            feature_type='cls'
        )  # [B, B]
        cls_similarity_pos = cls_similarity_matrix.diag()  # [B]

        cls_neg_similarity_matrix = model.calculate_similarity(
            query_features['cls_feature'],
            cls_negative_features['cls_feature'],
            feature_type='cls'
        )  # [B, B]
        cls_similarity_neg = cls_neg_similarity_matrix.diag()

        # ============ 重要代码块：用于调试相似度矩阵 ============
        # 说明：这段代码用于保存一个batch中的相似度矩阵，用于分析模型性能
        # 使用方法：取消注释以下代码块来保存相似度矩阵
        # 注意：执行后程序会立即退出，仅用于调试
        # WARNING: 请勿删除此代码块，它用于重要的调试功能
        #
        torch.save({
            'cls_pos_sim': cls_similarity_matrix,
            'cls_neg_sim': cls_neg_similarity_matrix,
            'pose_pos_sim': pose_similarity_matrix,
            # 'pose_neg_sim': pose_neg_similarity_matrix
        }, 'similarity_matrices.pth')
        print("相似度矩阵已保存到 similarity_matrices416.pth")
        exit(0)
        # import pickle
        # with open('data_mulit_negs.pkl','wb') as f:
        #     pickle.dump(miniBatch, f)
        # exit(0)
        # =====================================================
        
        # 计算损失（传入多个负样本的相似度和角度差异）
        loss_dict = criterion(
            positive_pair=pose_similarity_pos,  # [B]
            negative_pairs=pose_similarities_neg,  # [B, num_negatives]
            cls_pos_pair=cls_similarity_pos,  # [B]
            cls_neg_pair=cls_similarity_neg,  # [B]
            deltas=pose_negative_deltas  # [B, num_negatives]
        )

        # 更新度量
        meter_pose_pos_sim.update(loss_dict['pose']['pos_sim'])
        meter_pose_neg_sim.update(loss_dict['pose']['neg_sim'])
        meter_pose_loss.update(loss_dict['pose']['loss'])
        
        if 'cls' in loss_dict:
            meter_cls_pos_sim.update(loss_dict['cls']['pos_sim'])
            meter_cls_neg_sim.update(loss_dict['cls']['neg_sim'])
            meter_cls_loss.update(loss_dict['cls']['loss'])
        
        meter_total_loss.update(loss_dict['total_loss'].item())

        # 如果使用wandb，添加角度信息的记录
        if use_wandb:
            wandb.log({
                "pose/positive_similarity": loss_dict['pose']['pos_sim'],
                "pose/negative_similarity": loss_dict['pose']['neg_sim'],
                "pose/loss": loss_dict['pose']['loss'],
                "pose/angle_weight_mean": pose_negative_deltas.mean().item(),
                "total_loss": loss_dict['total_loss'].item(),
                "learning_rate": optimizer.param_groups[0]['lr']
            })
            if 'cls' in loss_dict:
                wandb.log({
                    "cls/positive_similarity": loss_dict['cls']['pos_sim'],
                    "cls/negative_similarity": loss_dict['cls']['neg_sim'],
                    "cls/loss": loss_dict['cls']['loss']
                })

        # 反向传播
        optimizer.zero_grad()
        loss_dict['total_loss'].backward()
        optimizer.step()

        # if i % 100 ==0 :
        #     torch.save({
        #         'cls_pos_sim': cls_similarity_matrix,
        #         'cls_neg_sim': cls_neg_similarity_matrix,
        #         'pose_pos_sim': pose_similarity_matrix,
        #         'pose_neg_sim': pose_neg_similarity_matrix
        #     }, f'similarity_matrices_epoch{epoch}.pth')
        #     print("相似度矩阵已保存到 similarity_matrices.pth")

        # 每隔一定步数打印训练信息
        if (i + 1) % log_interval == 0:
            logger.info(monitoring_text.format(
                epoch, i + 1, train_size,
                meter_total_loss.avg,
                meter_pose_loss.avg,
                meter_pose_pos_sim.avg,
                meter_pose_neg_sim.avg,
                meter_cls_loss.avg if 'cls' in loss_dict else 0.0,
                meter_cls_pos_sim.avg if 'cls' in loss_dict else 0.0,
                meter_cls_neg_sim.avg if 'cls' in loss_dict else 0.0
            ))
            
            # 每100个batch打印详细的相似度分布
            if i % 100 == 0:
                logger.info("\nDetailed similarity statistics:")
                logger.info("Pose task:")
                logger.info(f"  Positive similarity: {meter_pose_pos_sim.avg:.3f}")
                logger.info(f"  Negative similarity: {meter_pose_neg_sim.avg:.3f}")
                logger.info(f"  Loss: {meter_pose_loss.avg:.3f}")
                logger.info("Class task:")
                logger.info(f"  Positive similarity: {meter_cls_pos_sim.avg:.3f}")
                logger.info(f"  Negative similarity: {meter_cls_neg_sim.avg:.3f}")
                logger.info(f"  Loss: {meter_cls_loss.avg:.3f}")
                logger.info("")  # 空行分隔

    # 打印每个epoch的训练时间和最终统计信息
    end_time = time.time()
    logger.info(timing_text.format(epoch, (end_time - start_time) / 60))
    logger.info("\nEpoch {} final statistics:".format(epoch))
    logger.info(f"Total loss: {meter_total_loss.avg:.3f}")
    logger.info(f"Pose loss: {meter_pose_loss.avg:.3f} (pos:{meter_pose_pos_sim.avg:.3f}, neg:{meter_pose_neg_sim.avg:.3f})")
    logger.info(f"Class loss: {meter_cls_loss.avg:.3f} (pos:{meter_cls_pos_sim.avg:.3f}, neg:{meter_cls_neg_sim.avg:.3f})")

    
    return meter_total_loss.avg
