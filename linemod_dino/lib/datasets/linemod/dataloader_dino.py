import os, random
import numpy as np
from PIL import Image
import pandas as pd
import torch
import torch.utils.data as data
import json
from torchvision import transforms
from pathlib import Path

from lib.poses import utils
from lib.datasets import dataloader_utils
from lib.datasets.linemod import inout

np.random.seed(2024)
random.seed(2024)
number_train_template = 1542
number_test_template = 301


class LINEMODQuery(data.Dataset):
    def __init__(self, root_dir, dataset, list_id_obj, split, image_size, save_path, use_normalize=True, use_mask=False, gt_bbox_known=True, use_aug=False, aug_config=None, num_negatives=1):
        self.root_dir = root_dir
        self.dataset_name = dataset
        self.list_id_obj = list(list_id_obj)
        self.split = split
        self.image_size = image_size
        self.save_path = save_path
        self.use_normalize = use_normalize
        self.use_mask = use_mask
        self.gt_bbox_known = gt_bbox_known
        self.use_aug = use_aug
        self.num_negatives = num_negatives
        self.aug_config = aug_config or {
            "center_region_ratio": 0.5,
            "min_mask_ratio": 0.05,
            "max_mask_ratio": 0.3
        }
        
        # 根据use_normalize参数决定是否使用标准化
        transforms_list = [transforms.ToTensor()]
        if use_normalize:
            transforms_list.append(
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            )
        self.im_transform = transforms.Compose(transforms_list)
        
        self.query_data, self.template_data = self.get_data()
        print("Length of the dataset: {}".format(self.__len__()))
        self.save_random_sequences()

    def __len__(self):
        return len(self.query_data)

    def get_data(self):
        # load the query frame
        if "occ" in self.dataset_name.lower():
            json_name = "occlusionLINEMOD.json"
        else:
            json_name = "LINEMOD.json"
        list_files = os.path.join(self.root_dir, json_name)
        with open(list_files) as json_file:
            query_frame = json.load(json_file)
        query_frame = pd.DataFrame.from_dict(query_frame, orient='index')
        query_frame = query_frame.transpose()
        print("Id object available {}".format(sorted(query_frame['id_obj'].unique())))
        print("Taking only objects {}".format(self.list_id_obj))
        query_frame = query_frame[query_frame.id_obj.isin(self.list_id_obj)]
        query_frame = query_frame.sample(frac=1, random_state=2024).reset_index(drop=True)

        if "test" in self.split:
            if self.split == "seen_test":
                # take 10% to evaluate on seen objects (unseen poses)
                index_test = query_frame.groupby('id_obj').apply(dataloader_utils.sampling_k_samples
                                                                 ).index.get_level_values(1)
                index_test = np.asarray(index_test)
                query_frame = query_frame.iloc[index_test]
                query_frame = query_frame.sample(frac=1, random_state=2024).reset_index(drop=True)
                print("Split test seen: ", len(query_frame))
                return query_frame, None
            else:
                return query_frame, None
        else:
            index_test = query_frame.groupby('id_obj').apply(
                dataloader_utils.sampling_k_samples
            ).index.get_level_values(1)
            index_test = np.asarray(index_test)
            query_frame = query_frame.drop(index_test)
            query_frame = query_frame.sample(frac=1, random_state=2024).reset_index(drop=True)
            query_frame["synthetic_path"] = query_frame["train_template_path"]
            query_frame["synthetic_location"] = query_frame["train_template_location"]

            # load the training template frame
            list_path, list_poses, ids_obj, id_symmetry = [], [], [], []
            if os.path.exists("./lib/poses/predefined_poses/half_sphere_level2_and_level3.npy"):
                obj_poses = np.load("./lib/poses/predefined_poses/half_sphere_level2_and_level3.npy")
            else:
                obj_poses = np.load("../lib/poses/predefined_poses/half_sphere_level2_and_level3.npy")
            for id_obj in self.list_id_obj:
                if "occ" in self.dataset_name.lower():
                    obj_name = inout.occlusion_real_id_to_name[id_obj]
                else:
                    obj_name = inout.LINEMOD_real_id_to_name[id_obj]
                for id_frame in range(number_train_template):
                    list_path.append(os.path.join("templatesLINEMOD/train", obj_name, "{:06d}.png".format(id_frame)))
                    location = utils.opencv2opengl(np.asarray(obj_poses[id_frame]))[2, :3]
                    list_poses.append(location)
                    ids_obj.append(id_obj)
                    id_symmetry.append(inout.list_all_id_symmetry[id_obj])
            all_data = {"id_obj": ids_obj,
                        "id_symmetry": id_symmetry,
                        "obj_poses": list_poses,
                        "synthetic_path": list_path}
            template_frame = pd.DataFrame.from_dict(all_data, orient='index')
            template_frame = template_frame.transpose()

            # shuffle data
            template_frame = template_frame.sample(frac=1).reset_index(drop=True)
            print("Split seen training ", len(query_frame))
            return query_frame, template_frame

    def _apply_random_mask(self, img):
        """在图像中心区域随机添加白色mask
        Args:
            img: PIL Image对象
        Returns:
            PIL Image对象
        """
        # 转换为可修改的格式
        img = img.copy()
        
        # 计算中心区域
        width, height = img.size
        center_x = width // 2
        center_y = height // 2
        region_size = int(min(width, height) * self.aug_config["center_region_ratio"])
        
        # 计算mask的随机大小
        mask_ratio = random.uniform(
            self.aug_config["min_mask_ratio"],
            self.aug_config["max_mask_ratio"]
        )
        mask_size = int(region_size * mask_ratio)
        
        # 在中心区域内随机选择mask的位置
        x1 = random.randint(center_x - region_size//2, center_x + region_size//2 - mask_size)
        y1 = random.randint(center_y - region_size//2, center_y + region_size//2 - mask_size)
        x2 = x1 + mask_size
        y2 = y1 + mask_size
        
        # 在选定区域填充白色
        for y in range(y1, y2):
            for x in range(x1, x2):
                img.putpixel((x, y), (255, 255, 255))
        
        return img

    def _sample(self, idx, isQuery, isPositive=None, isDiff_obj=None):
        """
        Sampling function given that whether
        1. Image is query (or real image),
        2. Image have same pose as idx,
        3. Image is same object
        """
        if isQuery:
            # load query image
            rgb_path = self.query_data.iloc[idx]['real_path']
            dataset_dir = "occlusionLINEMOD" if "occ" in self.dataset_name.lower() else "LINEMOD"
            rgb_path = os.path.join(self.root_dir, "crop_image{}".format(self.image_size), dataset_dir, rgb_path)
            img = Image.open(rgb_path).convert('RGB')
            return img
        else:
            if isPositive:
                # 正样本（相同物体，相同姿态）
                if self.split == "train":
                    rgb_path = self.query_data.iloc[idx]['synthetic_path']
                else:
                    rgb_path = self.query_data.iloc[idx]['train_template_path']
                if "templatesLINEMOD" in rgb_path:
                    rgb_path = os.path.join(self.root_dir, "crop_image{}".format(self.image_size), rgb_path)
                else:
                    dataset_dir = "occlusionLINEMOD" if "occ" in self.dataset_name.lower() else "LINEMOD"
                    rgb_path = os.path.join(self.root_dir, "crop_image{}".format(self.image_size), dataset_dir, rgb_path)
                img = Image.open(rgb_path).convert('RGB')
                # 只在正样本时应用random mask
                if self.use_aug:
                    img = self._apply_random_mask(img)
                return img
            else:
                if isDiff_obj:
                    # 不同物体的负样本
                    id_obj = self.query_data.iloc[idx]['id_obj']
                    while True:
                        idx_random = np.random.randint(0, len(self.query_data))
                        if self.query_data.iloc[idx_random]['id_obj'] != id_obj:
                            break

                    # 50%概率使用真实图像，50%概率使用模板图像
                    if random.random() >= 0.5:
                        # 使用真实图像
                        rgb_path = self.query_data.iloc[idx_random]['real_path']
                        dataset_dir = "occlusionLINEMOD" if "occ" in self.dataset_name.lower() else "LINEMOD"
                        rgb_path = os.path.join(self.root_dir, "crop_image{}".format(self.image_size), dataset_dir, rgb_path)
                    else:
                        # 使用模板图像
                        rgb_path = self.query_data.iloc[idx_random]['synthetic_path']
                        if "templatesLINEMOD" in rgb_path:
                            rgb_path = os.path.join(self.root_dir, "crop_image{}".format(self.image_size), rgb_path)
                        else:
                            dataset_dir = "occlusionLINEMOD" if "occ" in self.dataset_name.lower() else "LINEMOD"
                            rgb_path = os.path.join(self.root_dir, "crop_image{}".format(self.image_size), dataset_dir, rgb_path)

                    img = Image.open(rgb_path).convert('RGB')
                    return img
                else:
                    # 同物体不同姿态的负样本
                    id_obj = self.query_data.iloc[idx]['id_obj']
                    
                    # 获取所有相同物体的模板索引
                    same_obj_indices = self.template_data[self.template_data['id_obj'] == id_obj].index
                    
                    # 随机选择多个不同姿态的负样本
                    selected_indices = np.random.choice(same_obj_indices, size=min(self.num_negatives, len(same_obj_indices)), replace=False)
                    
                    # 加载所有选中的负样本图像
                    negative_images = []
                    deltas = []
                    query_pose = np.asarray(self.query_data.iloc[idx]["real_location"])
                    
                    for idx_random in selected_indices:
                        rgb_path = self.template_data.iloc[idx_random]['synthetic_path']
                        rgb_path = os.path.join(self.root_dir, "crop_image{}".format(self.image_size), rgb_path)
                        img = Image.open(rgb_path).convert('RGB')
                        negative_images.append(img)
                        
                        # 计算角度差异
                        template_pose = np.asarray(self.template_data.iloc[idx_random]["obj_poses"])
                        division_term = np.linalg.norm(query_pose) * np.linalg.norm(template_pose)
                        # delta = np.clip(template_pose.dot(query_pose) / division_term, a_min=0, a_max=1)
                        delta = template_pose.dot(query_pose) / division_term
                        delta_degree = np.rad2deg(np.arccos(delta))
                        deltas.append(delta_degree)

                    return deltas, negative_images

    def _sample_triplet(self, idx, save_path=None):
        query = self._sample(idx, isQuery=True)
        template = self._sample(idx, isQuery=False, isPositive=True)
        negative_random = self._sample(idx, isQuery=False, isPositive=False, isDiff_obj=True)
        deltas, negative_same_objs = self._sample(idx, isQuery=False, isPositive=False, isDiff_obj=False)

        if save_path is None:
            return [self.im_transform(query)], [self.im_transform(template)], \
                   [self.im_transform(negative_random)], [self.im_transform(negative_same_objs[0]), deltas[0]]
        else:
            query.save(save_path + "_query.png")
            template.save(save_path + "_template.png")
            negative_random.save(save_path + "_negative_random.png")
            # 只保存第一个负样本用于可视化
            negative_same_objs[0].save(save_path + "_negative_same_obj.png")

    def _sample_mask(self, idx):
        """获取mask图像"""
        rgb_path = self.query_data.iloc[idx]['real_path']
        dataset_dir = "occlusionLINEMOD" if "occ" in self.dataset_name.lower() else "LINEMOD"
        mask_path = os.path.join(self.root_dir, "crop_image{}".format(self.image_size),
                                dataset_dir, rgb_path.replace(".png", "_mask.png"))
        mask = Image.open(mask_path).convert('L')  # 转换为灰度图
        return mask

    def _get_multiple_cls_negatives(self, idx, num_negatives=10):
        """获取多个不同类物体的负样本
        Args:
            idx: 当前查询图像的索引
            num_negatives: 需要获取的负样本数量
        Returns:
            tensor: 形状为[num_negatives, C, H, W]的张量，包含多个不同类物体图像
        """
        id_first_obj = self.query_data.iloc[idx]['id_obj']
        list_id_second_obj = self.list_id_obj.copy()
        list_id_second_obj.remove(id_first_obj)

        negative_images = []
        for _ in range(num_negatives):
            # 随机选择一个不同的物体
            id_second_obj = np.random.choice(list_id_second_obj)
            new_frame = self.query_data[self.query_data.id_obj == id_second_obj]
            idx_frame_second_obj = np.random.randint(0, len(new_frame))

            # 获取真实图像路径
            img_path = os.path.join(self.root_dir, "crop_image{}".format(self.image_size),
                                    self.dataset_name, new_frame.iloc[idx_frame_second_obj]['real_path'])
            img = Image.open(img_path).convert('RGB')
            img = self.im_transform(img)

            if self.use_mask:
                # 获取并处理mask
                mask_path = img_path.replace(".png", "_mask.png")
                mask = Image.open(mask_path).convert('L')  # 转换为灰度图
                # 将mask调整到与图像相同的大小
                mask = transforms.Resize((self.image_size, self.image_size))(transforms.ToTensor()(mask))
                # 将mask应用到图像上
                img = img * mask

            negative_images.append(img)

        # 将列表堆叠成tensor
        return torch.stack(negative_images)

    def __getitem__(self, idx):
        """获取一个训练样本
        Args:
            idx: 样本索引
        Returns:
            dict: 包含查询图像、正样本和负样本的字典
        """
        # 获取基本信息
        id_obj = self.query_data.iloc[idx]['id_obj']
        id_symmetry = inout.list_all_id_symmetry[id_obj]
        obj_pose = torch.from_numpy(np.asarray(self.query_data.iloc[idx]["real_location"]))

        if not self.split == "train":
            # 测试模式只返回基本信息和查询图像
            query = self._sample(idx, isQuery=True)
            query = self.im_transform(query)
            if self.use_mask:
                # 获取并处理mask
                mask = self._sample_mask(idx)
                mask = transforms.ToTensor()(mask)
                # 将mask应用到query图像上
                query = query * mask

            # 只有在不使用gt_bbox时才获取多个负样本
            if not self.gt_bbox_known:
                cls_negatives = self._get_multiple_cls_negatives(idx, num_negatives=5)
                other_negatives = []
                for _ in range(5):
                    try:
                        other_image_path = os.path.join(self.root_dir, 'crop_image{}'.format(self.image_size), 'others', 'crop_{:04d}.jpg'.format(random.randint(0, 1000)))
                        if os.path.exists(other_image_path):
                            other_image = Image.open(other_image_path).convert('RGB')
                            other_image = self.im_transform(other_image)
                            other_negatives.append(other_image)
                    except Exception as e:
                        print(f"Warning: Failed to load other image: {e}")
                        continue
                if other_negatives:
                    other_negatives = torch.stack(other_negatives)
                    cls_negatives = torch.cat([cls_negatives, other_negatives], dim=0)
                    
                return dict(id_obj=id_obj, id_symmetry=id_symmetry, obj_pose=obj_pose, query=query, cls_negatives=cls_negatives)
            else:
                return dict(id_obj=id_obj, id_symmetry=id_symmetry, obj_pose=obj_pose, query=query)

        # 训练模式返回完整的训练数据
        query = self._sample(idx, isQuery=True)
        template = self._sample(idx, isQuery=False, isPositive=True)
        cls_negative = self._sample(idx, isQuery=False, isPositive=False, isDiff_obj=True)
        deltas, pose_negatives = self._sample(idx, isQuery=False, isPositive=False, isDiff_obj=False)
        
        # 转换图像
        query = self.im_transform(query)
        if self.use_mask:
            # 获取并处理mask
            mask = self._sample_mask(idx)
            mask = transforms.ToTensor()(mask)
            # 将mask应用到query图像上
            query = query * mask
        
        template = self.im_transform(template)
        cls_negative = self.im_transform(cls_negative)
        pose_negatives = [self.im_transform(img) for img in pose_negatives]
        
        # 构建返回字典
        return dict(
            id_obj=id_obj,
            id_symmetry=id_symmetry,
            obj_pose=obj_pose,
            query=query,
            template=template,
            pose_negatives=pose_negatives,
            pose_negative_deltas=torch.tensor(deltas, dtype=torch.float32),
            cls_negative=cls_negative
        )

    def save_random_sequences(self):
        len_data = self.__len__()
        list_index = np.unique(np.random.randint(0, len_data, 10))
        print("Saving samples at {}".format(self.save_path))
        if not os.path.exists(self.save_path):
            os.makedirs(self.save_path)
        for idx in list_index:
            save_path = os.path.join(self.save_path, "{:06d}".format(idx))
            if self.split == "train":
                self._sample_triplet(idx, save_path)
            else:
                query = self._sample(idx, isQuery=True)
                query.save(save_path + "_test.png")


class TemplatesLINEMOD(LINEMODQuery):
    def __init__(self, root_dir, dataset, list_id_obj, split, image_size, save_path, use_normalize=True):
        self.root_dir = root_dir
        self.dataset_name = dataset
        self.list_id_obj = list(list_id_obj)
        self.split = split
        assert self.split == "test", print("Split should be test")
        self.image_size = image_size
        self.save_path = save_path

        # 根据use_normalize参数决定是否使用标准化
        transforms_list = [transforms.ToTensor()]
        if use_normalize:
            transforms_list.append(
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            )
        self.im_transform = transforms.Compose(transforms_list)

        self.query_data, self.template_data = self.get_data()
        print("Length of the dataset: {}".format(self.__len__()))
        self.save_random_sequences()

    def get_data(self):
        list_path, list_poses, ids_obj, id_symmetry = [], [], [], []
        if os.path.exists("./lib/poses/predefined_poses/half_sphere_level2.npy"):
            obj_poses = np.load("./lib/poses/predefined_poses/half_sphere_level2.npy")
        else:
            obj_poses = np.load("../lib/poses/predefined_poses/half_sphere_level3.npy")
        obj_locations = []
        for id_frame in range(number_test_template):
            location = utils.opencv2opengl(np.asarray(obj_poses[id_frame]))[2, :3]
            obj_locations.append(torch.from_numpy(np.round(location, 4).reshape(3)))

        for id_obj in self.list_id_obj:
            obj_name = inout.LINEMOD_real_id_to_name[id_obj]
            for id_frame in range(number_test_template):
                list_path.append(os.path.join("templatesLINEMOD/test", obj_name, "{:06d}.png".format(id_frame)))
                list_poses.append(obj_locations[id_frame].reshape(-1, 3))
                ids_obj.append(id_obj)
                id_symmetry.append(inout.list_all_id_symmetry[id_obj])
            all_data = {"id_obj": ids_obj,
                        "id_symmetry": id_symmetry,
                        "obj_poses": list_poses,
                        "synthetic_path": list_path}
        template_frame = pd.DataFrame.from_dict(all_data, orient='index')
        template_frame = template_frame.transpose()
        return template_frame, template_frame

    def _sample(self, idx, isQuery=False, isPositive=True):
        img = None
        if isPositive:
            rgb_path = self.query_data.iloc[idx]['synthetic_path']
            rgb_path = os.path.join(self.root_dir, "crop_image{}".format(self.image_size), rgb_path)
            img = Image.open(rgb_path).convert('RGB')
        return img

    def _sample_template(self, idx, save_path=None):
        img = self._sample(idx, isQuery=False, isPositive=True)
        if save_path is None:
            return [self.im_transform(img)]
        else:
            img.save(save_path + ".png")

    def __getitem__(self, idx):
        id_obj = self.query_data.iloc[idx]['id_obj']
        id_symmetry = inout.list_all_id_symmetry[id_obj]
        obj_pose = self.query_data.iloc[idx]["obj_poses"].reshape(3)
        template = self._sample(idx, isQuery=False, isPositive=True)
        template = self.im_transform(template)
        return dict(id_obj=id_obj, id_symmetry=id_symmetry, obj_pose=obj_pose,
                   template=template)

    def save_random_sequences(self):
        len_data = self.__len__()
        list_index = np.unique(np.random.randint(0, len_data, 10))
        print("Saving samples at {}".format(self.save_path))
        if not os.path.exists(self.save_path):
            os.makedirs(self.save_path)
        for idx in list_index:
            save_path = os.path.join(self.save_path, "{:06d}".format(idx))
            self._sample_template(idx, save_path)