import os
from torch.utils.data import DataLoader
import torch


def init_dataloader(dict_dataloader, batch_size, num_workers):
    """
    初始化数据加载器
    Args:
        dict_dataloader: 数据集字典
        batch_size: batch大小
        num_workers: 工作进程数
    """
    for key in dict_dataloader.keys():
        if key == "train" or "template" in key:  # 训练集和模板数据集使用指定的batch_size
            _batch_size = batch_size
        else:  # 其他数据集使用batch_size=1
            _batch_size = 1
            
        dict_dataloader[key] = torch.utils.data.DataLoader(
            dict_dataloader[key],
            batch_size=_batch_size,
            shuffle=True if key == "train" else False,  # 只有训练集需要shuffle
            num_workers=num_workers,
            pin_memory=True
        )
    return dict_dataloader


def write_txt(path, list_files):
    with open(path, "w") as f:
        for idx in list_files:
            f.write(idx + "\n")
        f.close()


def get_list_background_img_from_dir(background_dir):
    if not os.path.exists(os.path.join(background_dir, "list_img.txt")):
        jpgs = [os.path.join(root, file) for root, dirs, files in os.walk(background_dir)
                for file in files if file.endswith('.jpg')]
        write_txt(os.path.join(background_dir, "list_img.txt"), jpgs)
    else:
        with open(os.path.join(background_dir, "list_img.txt"), 'r') as f:
            jpgs = [x.strip() for x in f.readlines()]
    return jpgs


def sampling_k_samples(group, k=109):
    if len(group) < k:
        return group
    return group.sample(k, random_state=2024)