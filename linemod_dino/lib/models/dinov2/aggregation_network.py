import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np


class MultiLayerTransformer(nn.Module):
    def __init__(self, input_dim, descriptor_size, num_heads=8, device="cuda", use_residual=True):
        super().__init__()
        self.input_dim = input_dim
        self.descriptor_size = descriptor_size
        self.device = device
        self.use_residual = use_residual
        
        # 投影层：将每层的patch tokens投影到统一的维度
        self.patch_projection = nn.Linear(input_dim, descriptor_size).to(device)
        
        # 层间注意力
        self.layer_attention = nn.MultiheadAttention(
            embed_dim=descriptor_size,
            num_heads=num_heads,
            batch_first=True
        ).to(device)
        
        # Layer Norm
        self.norm1 = nn.LayerNorm(descriptor_size).to(device)
        self.norm2 = nn.LayerNorm(descriptor_size).to(device)
        
        # FFN
        self.ffn = nn.Sequential(
            nn.Linear(descriptor_size, descriptor_size * 4),
            nn.GELU(),
            nn.Linear(descriptor_size * 4, descriptor_size)
        ).to(device)
        
        # 添加pose token的处理组件
        self.pose_projection = nn.Linear(input_dim, descriptor_size).to(device)
        self.pose_attention = nn.MultiheadAttention(
            embed_dim=descriptor_size,
            num_heads=num_heads,
            batch_first=True
        ).to(device)
        
        # 初始化位置编码缓存
        self.pos_enc_cache = {}
        
    def _get_position_encoding(self, seq_len):
        """获取位置编码，如果已经计算过则直接返回缓存的结果
        Args:
            seq_len: 序列长度
        Returns:
            position_enc: [1, seq_len, input_dim]
        """
        if seq_len not in self.pos_enc_cache:
            position = torch.arange(seq_len, dtype=torch.float32).unsqueeze(1)
            div_term = torch.exp(torch.arange(0, self.input_dim, 2, dtype=torch.float32) * 
                               (-np.log(10000.0) / self.input_dim))
            
            pe = torch.zeros(1, seq_len, self.input_dim)
            pe[0, :, 0::2] = torch.sin(position * div_term)
            pe[0, :, 1::2] = torch.cos(position * div_term)
            
            self.pos_enc_cache[seq_len] = pe.to(self.device)
            
        return self.pos_enc_cache[seq_len]
        
    def forward(self, patch_tokens_list, pose_tokens_list=None):
        """处理多层patch tokens和可选的pose tokens
        Args:
            patch_tokens_list: list of [B, C, L] tensors
            pose_tokens_list: list of [B, num_tokens, C] tensors (可选)
        Returns:
            processed_features: [B, descriptor_size] tensor
        """
        processed_features = []
        for i, patch_tokens in enumerate(patch_tokens_list):
            # 调整维度顺序：[B, C, L] -> [B, L, C]
            if patch_tokens.shape[1] == self.input_dim:
                patch_tokens = patch_tokens.transpose(1, 2)  # [B, L, C]
            
            # 先添加位置编码
            L = patch_tokens.shape[1]
            pos_enc = self._get_position_encoding(L)  # [1, L, C]
            patch_tokens = patch_tokens + pos_enc  # [B, L, C]
            
            # 再投影到统一维度
            projected = self.patch_projection(patch_tokens)  # [B, L, descriptor_size]
            
            # 如果有pose tokens，进行cross attention
            if pose_tokens_list is not None:
                pose_tokens = pose_tokens_list[i]
                # 投影pose tokens
                if pose_tokens.shape[-1] == self.input_dim:
                    pose_tokens = self.pose_projection(pose_tokens)  # [B, num_tokens, descriptor_size]
                
                # Cross attention: pose tokens作为query
                attn_output, _ = self.pose_attention(
                    pose_tokens, projected, projected
                )  # [B, num_tokens, descriptor_size]
                
                # 残差连接和Layer Norm
                if self.use_residual:
                    attn_output = self.norm1(attn_output + pose_tokens)
                else:
                    attn_output = self.norm1(attn_output)
                
                # FFN
                ffn_output = self.ffn(attn_output)
                if self.use_residual:
                    processed = self.norm2(ffn_output + attn_output)
                else:
                    processed = self.norm2(ffn_output)
                
                # 取平均得到当前层的特征
                processed = processed.mean(dim=1)  # [B, descriptor_size]
            else:
                # 没有pose tokens时，直接对patch tokens进行处理
                # 使用注意力机制进行自注意力
                attn_output, _ = self.layer_attention(
                    projected, projected, projected
                )  # [B, L, descriptor_size]
                
                # 残差连接和Layer Norm
                if self.use_residual:
                    attn_output = self.norm1(attn_output + projected)
                else:
                    attn_output = self.norm1(attn_output)
                
                # FFN
                ffn_output = self.ffn(attn_output)
                if self.use_residual:
                    processed = self.norm2(ffn_output + attn_output)
                else:
                    processed = self.norm2(ffn_output)
                
                # 取平均得到当前层的特征
                processed = processed.mean(dim=1)  # [B, descriptor_size]
            
            processed_features.append(processed)
        
        # 将所有层的特征取平均
        processed_features = torch.stack(processed_features, dim=0).mean(dim=0)  # [B, descriptor_size]
        return processed_features


class AggregationNetwork(nn.Module):
    def __init__(self, descriptor_size, feature_dims, device="cuda", input_dim=768, use_pose_tokens=False, use_residual=True):
        super().__init__()
        self.descriptor_size = descriptor_size
        self.feature_dims = feature_dims
        self.device = device
        self.use_pose_tokens = use_pose_tokens
        
        # 多层特征处理
        self.multi_layer_transformer = MultiLayerTransformer(
            input_dim=input_dim,
            descriptor_size=descriptor_size,
            device=device,
            use_residual=use_residual
        )
        
        # 共享的投影层
        self.projection = nn.Sequential(
            nn.Linear(input_dim, input_dim * 2),
            nn.ReLU(),
            nn.Linear(input_dim * 2, descriptor_size)
        ).to(device)
        
        # 共享的注意力层
        self.attention = nn.MultiheadAttention(
            embed_dim=descriptor_size,
            num_heads=8,
            batch_first=True
        ).to(device)
        
        # 共享的Layer Norm
        self.norm1 = nn.LayerNorm(descriptor_size).to(device)
        self.norm2 = nn.LayerNorm(descriptor_size).to(device)
        
        # 共享的FFN
        self.ffn = nn.Sequential(
            nn.Linear(descriptor_size, descriptor_size * 4),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(descriptor_size * 4, descriptor_size)
        ).to(device)
        
        # 简单聚合，用于没有pose tokens时的处理
        self.simple_aggregation = nn.Sequential(
            nn.Linear(input_dim, input_dim * 2),
            nn.LayerNorm(input_dim * 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(input_dim * 2, input_dim * 2),
            nn.LayerNorm(input_dim * 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(input_dim * 2, descriptor_size)
        ).to(device)
        
        # 初始化权重
        self._init_weights()
        
    def _init_weights(self):
        """使用Xavier初始化权重"""
        def init_weights(m):
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
        
        self.projection.apply(init_weights)
        self.ffn.apply(init_weights)
        self.simple_aggregation.apply(init_weights)
    
    def forward_cls(self, cls_tokens, pose_tokens=None):
        """处理CLS tokens
        Args:
            cls_tokens: [B, num_layers, C] tensor
            pose_tokens: [B, num_tokens, C] tensor (可选)
        Returns:
            cls_feature: [B, descriptor_size] tensor
        """
        if pose_tokens is not None:
            # 投影tokens到descriptor_size维度
            cls_tokens = self.projection(cls_tokens)  # [B, num_layers, descriptor_size]
            pose_tokens = self.projection(pose_tokens)  # [B, num_tokens, descriptor_size]
            
            # 使用pose tokens作为query，与CLS tokens进行attention
            query = pose_tokens  # [B, num_tokens, descriptor_size]
            key = value = cls_tokens  # [B, num_layers, descriptor_size]
            
            # Cross attention
            attn_output, _ = self.attention(query, key, value)  # [B, num_tokens, descriptor_size]
            
            # 取平均得到一个特征向量
            cls_feature = attn_output.mean(dim=1)  # [B, descriptor_size]
            
            # 残差连接和Layer Norm
            cls_feature = self.norm1(cls_feature)
            
            # FFN
            ffn_output = self.ffn(cls_feature)
            cls_feature = self.norm2(cls_feature + ffn_output)
            
        else:
            # 如果没有pose tokens，简单地聚合所有层的CLS tokens
            B, L, C = cls_tokens.shape
            cls_tokens_avg = cls_tokens.mean(dim=1)  # [B, C]
            cls_feature = self.simple_aggregation(cls_tokens_avg)  # [B, descriptor_size]
            
        return cls_feature
    
    def forward_pose(self, patch_tokens, pose_tokens=None):
        """处理patch tokens和pose tokens
        Args:
            patch_tokens: list of [B, C, L] tensors
            pose_tokens: list of [B, num_tokens, C] tensors (可选)
        Returns:
            pose_feature: [B, descriptor_size] tensor
        """
        if self.use_pose_tokens and pose_tokens is not None:
            # 使用pose tokens的处理逻辑
            pose_features = self.multi_layer_transformer(patch_tokens, pose_tokens)
        else:
            # 不使用pose tokens的处理逻辑
            pose_features = self.multi_layer_transformer(patch_tokens)
        
        return pose_features
    
    def forward(self, patch_tokens, pose_tokens=None, cls_tokens=None):
        """整合所有特征处理
        Args:
            patch_tokens: list of [B, C, L] tensors
            pose_tokens: list of [B, num_tokens, C] tensors (可选)
            cls_tokens: [B, num_layers, C] tensor (可选)
        Returns:
            dict: {
                'pose_feature': [B, descriptor_size] tensor,
                'cls_feature': [B, descriptor_size] tensor (如果提供了cls_tokens)
            }
        """
        # 处理pose feature
        pose_feature = self.forward_pose(patch_tokens, pose_tokens)
        
        # 处理cls feature（如果提供）
        result = {'pose_feature': pose_feature}
        if cls_tokens is not None:
            cls_feature = self.forward_cls(cls_tokens, pose_tokens[-1] if pose_tokens else None)
            result['cls_feature'] = cls_feature
        
        return result

    def calculate_similarity(self, query_features, template_features, feature_type='pose'):
        """计算特征的相似度
        Args:
            query_features: [B, descriptor_size] 或 dict
            template_features: [B, descriptor_size] 或 dict
            feature_type: str, 'pose' 或 'cls'，指定使用哪种特征
        Returns:
            similarity: [B, B] tensor
        """
        if isinstance(query_features, dict):
            query_features = query_features[f'{feature_type}_feature']
        if isinstance(template_features, dict):
            template_features = template_features[f'{feature_type}_feature']
            
        # 计算余弦相似度
        similarity = F.cosine_similarity(
            query_features.unsqueeze(1),  # [B, 1, D]
            template_features.unsqueeze(0),  # [1, B, D]
            dim=2
        )  # [B, B]
        
        return similarity 