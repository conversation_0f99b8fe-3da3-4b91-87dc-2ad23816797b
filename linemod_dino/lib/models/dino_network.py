import torch
import torch.nn as nn
import torch.nn.functional as F

class DINOv2Extractor(nn.Module):
    def __init__(self, device="cuda", output_resolution=32, pose_tokens_config=None, feature_blocks_config=None, version="dinov2_vitb14"):
        super().__init__()
        print("Loading pretrained DINO v2 model...")
        
        # DINOv2模型版本及其对应的特征维度
        self.model_dims = {
            "dinov2_vits14_reg": 384,
            "dinov2_vitb14_reg": 768,
            "dinov2_vitl14_reg": 1024,
            "dinov2_vitg14_reg": 1536
        }
        
        if version not in self.model_dims:
            raise ValueError(f"Unsupported DINOv2 version: {version}. Must be one of {list(self.model_dims.keys())}")
            
        # 加载指定版本的模型
        self.model = torch.hub.load('/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main', version, source='local')
        self.model.to(device)
        self.model.eval()  # 设置为评估模式

        # 获取当前模型的特征维度
        self.feature_dim = self.model_dims[version]
        print(f"Using {version} with feature dimension {self.feature_dim}")
        
        self.output_resolution = output_resolution
        self.device = device
        
        # 设置特征提取层
        self.feature_blocks_config = feature_blocks_config or {'indices': [9, 10, 11, 12]}
        
        # 获取模型的总层数
        total_blocks = len(self.model.blocks)
        print(f"Total number of transformer blocks: {total_blocks}")
        
        # 检查索引是否有效
        for idx in self.feature_blocks_config['indices']:
            if not isinstance(idx, int):
                raise ValueError(f"Block index must be integer, got {type(idx)}")
            if idx < 1 or idx > total_blocks:
                raise ValueError(f"Block index {idx} is out of range. Must be between 1 and {total_blocks}")
        
        # 检查索引是否有重复
        if len(set(self.feature_blocks_config['indices'])) != len(self.feature_blocks_config['indices']):
            raise ValueError("Duplicate block indices are not allowed")
            
        # 将1-based索引转换为0-based
        self.block_indices = [i-1 for i in sorted(self.feature_blocks_config['indices'])]
        print(f"Using transformer blocks: {[i+1 for i in self.block_indices]}")
        
        # 初始化可学习的pose tokens
        self.use_pose_tokens = False
        if pose_tokens_config is not None and pose_tokens_config.get('enabled', False):
            self.use_pose_tokens = True
            num_tokens = pose_tokens_config.get('num_tokens', 5)
            # 初始化可学习的pose tokens [num_tokens, feature_dim]
            self.learnable_tokens = nn.Parameter(
                torch.randn(num_tokens, self.feature_dim) * 0.02
            )
            print(f"Initialized {num_tokens} learnable tokens with dimension {self.feature_dim}")
            
            # 冻结DINO参数，但保持可学习tokens可训练
            for param in self.model.parameters():
                param.requires_grad = False
            print("DINO v2 model frozen, only learnable tokens will be updated during training")
        else:
            # 如果不使用可学习tokens，冻结所有参数
            for param in self.model.parameters():
                param.requires_grad = False
            print("DINO v2 model loaded and frozen.")
            
    def forward(self, x):
        # 调整图像大小为224x224（DINO v2的标准输入大小）
        x = F.interpolate(x, size=(224, 224), mode='bilinear', align_corners=False)
        
        # 存储不同类型的tokens
        cls_tokens_list = []
        pose_tokens_list = []
        patch_tokens_list = []
        
        def hook_fn(module, input, output, use_pose_tokens=False):
            """统一的hook函数，返回三种不同的tokens
            Args:
                output: [batch_size, seq_len, hidden_size]
                use_pose_tokens: 是否使用可学习的tokens
            Returns:
                分别存储三种tokens到对应的列表中
            """
            # 获取CLS token
            cls_token = output[:, 0:1, :]  # [B, 1, C]
            cls_token = cls_token.transpose(1, 2)  # [B, C, 1]
            cls_tokens_list.append(cls_token)
            
            # 获取patch tokens的起始位置
            start_patches = 1 + self.model.num_register_tokens  # CLS token + register tokens
            
            if use_pose_tokens:
                # 获取learnable tokens
                end_learnable = start_patches + self.learnable_tokens.shape[0]
                pose_tokens = output[:, start_patches:end_learnable, :]  # [B, num_tokens, C]
                pose_tokens = pose_tokens.transpose(1, 2)  # [B, C, num_tokens]
                # 获取patch tokens
                patch_tokens = output[:, end_learnable:, :]  # [B, num_patches, C]
            else:
                pose_tokens = None
                # 直接获取patch tokens
                patch_tokens = output[:, start_patches:, :]  # [B, num_patches, C]
            
            # 转换patch tokens的维度顺序
            patch_tokens = patch_tokens.transpose(1, 2)  # [B, C, L]
            # 存储tokens
            pose_tokens_list.append(pose_tokens)
            patch_tokens_list.append(patch_tokens)
        
        if self.use_pose_tokens:
            # 获取patch embeddings
            patch_embed = self.model.patch_embed(x)  # [B, num_patches, hidden_dim]
            B = x.shape[0]
            
            # 准备tokens
            cls_token = self.model.cls_token.expand(B, -1, -1)  # [B, 1, hidden_dim]
            register_tokens = self.model.register_tokens.expand(B, -1, -1)  # [B, num_register_tokens, hidden_dim]
            learnable_tokens_batch = self.learnable_tokens.unsqueeze(0).expand(B, -1, -1)  # [B, num_tokens, hidden_dim]
            
            # 拼接所有tokens: [CLS] + register_tokens + learnable_tokens + patch_tokens
            tokens = torch.cat([cls_token, register_tokens, learnable_tokens_batch, patch_embed], dim=1)
            
            # 添加位置编码（只给patch tokens添加）
            num_patches = patch_embed.shape[1]
            tokens[:, :1+self.model.num_register_tokens+self.learnable_tokens.shape[0], :] += 0  # 前面的tokens不加位置编码
            tokens[:, 1+self.model.num_register_tokens+self.learnable_tokens.shape[0]:, :] += self.model.pos_embed[:, 1:1+num_patches]  # 只给patch tokens加位置编码
            
            # 注册钩子到transformer块
            hooks = []
            for idx in self.block_indices:
                hooks.append(self.model.blocks[idx].register_forward_hook(
                    lambda module, input, output: hook_fn(module, input, output, use_pose_tokens=True)
                ))
            
            # 通过transformer blocks进行特征提取和交互
            for i, block in enumerate(self.model.blocks):
                tokens = block(tokens)
            
            # 移除钩子
            for hook in hooks:
                hook.remove()
                
        else:
            # 注册钩子到transformer块
            hooks = []
            for idx in self.block_indices:
                hooks.append(self.model.blocks[idx].register_forward_hook(
                    lambda module, input, output: hook_fn(module, input, output, use_pose_tokens=False)
                ))
            
            # 正常的DINO v2前向传播
            with torch.no_grad():
                _ = self.model(x)
            
            # 移除钩子
            for hook in hooks:
                hook.remove()
        
        return cls_tokens_list, pose_tokens_list, patch_tokens_list 