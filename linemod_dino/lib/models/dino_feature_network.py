import torch
import torch.nn as nn
import torch.nn.functional as F

from lib.losses.contrast_loss import InfoNCE, OcclusionAwareSimilarity
from lib.models.base_network import BaseFeatureExtractor


class DINOv2FeatureExtractor(nn.Module):
    def __init__(self, config, threshold, dino_extractor, aggregation_network):
        super().__init__()
        self.dino_extractor = dino_extractor
        self.aggregation_network = aggregation_network
        self.threshold = threshold
        self.config = config
        
    def forward(self, x):
        # 获取所有类型的tokens
        cls_tokens, pose_tokens, patch_tokens = self.dino_extractor(x)
        
        # 处理所有层的CLS Token
        cls_tokens_stacked = torch.stack([token.squeeze(-1) for token in cls_tokens], dim=1)  # [B, num_layers, C]
        
        # 处理所有层的patch tokens
        patch_tokens_processed = []
        for patch_token in patch_tokens:
            # 确保patch token的维度正确 [B, C, L]
            if patch_token.dim() == 3 and patch_token.shape[1] != self.dino_extractor.feature_dim:
                patch_token = patch_token.transpose(1, 2)
            patch_tokens_processed.append(patch_token)
            
        # 处理所有层的pose tokens（如果使用）
        if self.dino_extractor.use_pose_tokens:
            pose_tokens_processed = []
            for pose_token in pose_tokens:
                # 确保pose token的维度正确 [B, num_tokens, C]
                if pose_token.dim() == 2:  # [B, C]
                    pose_token = pose_token.unsqueeze(1)  # [B, 1, C]
                elif pose_token.dim() == 3 and pose_token.shape[1] == self.dino_extractor.feature_dim:
                    pose_token = pose_token.transpose(1, 2)  # [B, num_tokens, C]
                pose_tokens_processed.append(pose_token)
        else:
            pose_tokens_processed = None
            
        # 使用整合后的AggregationNetwork处理所有特征
        features = self.aggregation_network(
            patch_tokens=patch_tokens_processed,
            pose_tokens=pose_tokens_processed,
            cls_tokens=cls_tokens_stacked
        )
        
        return features
    
    def calculate_similarity(self, query_features, template_features, feature_type='pose'):
        """计算特征的相似度
        Args:
            query_features: [B, descriptor_size] 或 dict
            template_features: [B, descriptor_size] 或 dict
            feature_type: str, 'pose' 或 'cls'，指定使用哪种特征
        Returns:
            similarity: [B, B] tensor
        """
        if isinstance(query_features, dict):
            query_features = query_features[f'{feature_type}_feature']
        if isinstance(template_features, dict):
            template_features = template_features[f'{feature_type}_feature']
            
        # 在计算相似度时进行L2归一化
        query_features = F.normalize(query_features, p=2, dim=-1)
        template_features = F.normalize(template_features, p=2, dim=-1)
            
        # 计算余弦相似度
        similarity = torch.matmul(
            query_features,  # [B, D]
            template_features.t()  # [D, B]
        )  # [B, B]
        
        return similarity 