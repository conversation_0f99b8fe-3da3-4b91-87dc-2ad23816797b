import argparse
import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import torch
from tqdm import tqdm
import datetime
import torch.nn as nn
import logging

from lib.utils import weights, metrics, logger
from lib.utils.optimizer import adjust_learning_rate
from lib.datasets.dataloader_utils import init_dataloader
from lib.utils.config import Config

from lib.models.dino_network import DINOv2Extractor
from lib.models.dinov2.aggregation_network import AggregationNetwork
from lib.models.dino_feature_network import DINOv2FeatureExtractor
from lib.datasets.linemod.dataloader_dino import LINEMODQuery, TemplatesLINEMOD
from lib.datasets.im_transform import im_transform, tensor2im
from lib.datasets.linemod import inout
from lib.datasets.linemod import training_utils_dino
from lib.datasets.linemod import testing_utils_dino
import shutup
shutup.please()
import wandb

# 设置CUDA设备
# os.environ['CUDA_VISIBLE_DEVICES'] = '0'

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--config_path', type=str, default='configs/linemod_dino.json')
    parser.add_argument('--use_wandb', action='store_true')
    parser.add_argument('--wandb_name', type=str, help='指定运行的名称，如果不指定则使用默认格式：配置文件名_时间戳')
    parser.add_argument('--checkpoint', type=str, default=None, help='加载预训练模型的路径')
    parser.add_argument('--result_path', type=str, default='results/vis/linemod')

    args = parser.parse_args()

    dir_name = (args.config_path.split('/')[-1]).split('.')[0]
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    run_name = f"{dir_name}_{timestamp}"
    print("config", run_name)

    # 生成运行名称
    if args.wandb_name:
        wandb_name = args.wandb_name
    else:
        wandb_name = f"{dir_name}_{timestamp}"

    # 设置日志目录
    log_dir = os.path.join('logs', run_name)
    os.makedirs(log_dir, exist_ok=True)

    # 加载配置
    config_global = Config(config_file="configs/config.json").get_config()
    config_run = Config(args.config_path).get_config()

    # 初始化日志
    save_path = os.path.join(config_global.root_path, config_run.log.weights, run_name)
    trainer_dir = os.path.join(os.getcwd(), "logs")
    trainer_logger = logger.init_logger(save_path=save_path,
                                    trainer_dir=trainer_dir,
                                    trainer_logger_name=run_name)

    # 初始化模型
    dino_extractor = DINOv2Extractor(
        device="cuda",
        output_resolution=config_run.model.output_resolution,
        pose_tokens_config=config_run.model.pose_tokens,
        feature_blocks_config=config_run.model.feature_blocks,
        version=config_run.model.version
    )

    # 获取特征维度
    feature_dim = dino_extractor.feature_dim
    num_blocks = len(config_run.model.feature_blocks['indices'])
    feature_dims = [feature_dim] * num_blocks

    # 初始化聚合网络
    aggregation_network = AggregationNetwork(
        descriptor_size=config_run.model.descriptor_size,
        feature_dims=feature_dims,
        device="cuda",
        input_dim=feature_dim,
        use_pose_tokens=config_run.model.pose_tokens.get('use_attention', False),  # 是否使用pose tokens进行自注意力
        use_residual=config_run.model.get('use_residual', True)  # 是否使用残差连接，默认为True
    )
    model = DINOv2FeatureExtractor(
        config=config_run,
        threshold=0.2,
        dino_extractor=dino_extractor,
        aggregation_network=aggregation_network,
    ).cuda()

    if args.checkpoint is not None:
        print("Loading checkpoint from:", args.checkpoint)
        checkpoint = torch.load(args.checkpoint)
        if 'model' in checkpoint:
            model.aggregation_network.load_state_dict(checkpoint['model'])
        if 'learnable_tokens' in checkpoint and model.dino_extractor.use_pose_tokens:
            model.dino_extractor.learnable_tokens = checkpoint['learnable_tokens']

    # 加载数据
    transforms = im_transform()
    seen_id_obj, seen_names, seen_occ_id_obj, seen_occ_names, unseen_id_obj, unseen_names, \
        unseen_occ_id_obj, unseen_occ_names = inout.get_list_id_obj_from_split_name(config_run.dataset.split)

    # 定义数据加载器配置
    config_loader = [["train", "train", "LINEMOD", seen_id_obj],
                    ["seen_test", "seen_test", "LINEMOD", seen_id_obj],
                    ["seen_template", "test", "templatesLINEMOD", seen_id_obj],
                    ["unseen_test", "test", "LINEMOD", unseen_id_obj],
                    ["unseen_template", "test", "templatesLINEMOD", unseen_id_obj]]
                    # ["seen_occ_test", "test", "occlusionLINEMOD", seen_occ_id_obj],
                    # ["seen_occ_template", "test", "templatesLINEMOD", seen_occ_id_obj],
                    # ["unseen_occ_test", "test", "occlusionLINEMOD", unseen_occ_id_obj],
                    # ["unseen_occ_template", "test", "templatesLINEMOD", unseen_occ_id_obj]]

    # 创建所有数据集
    datasetLoader = {}
    for config in config_loader:
        print("Dataset", config[0], config[2], config[3])
        if config[2] == "templatesLINEMOD":
            save_sample_path = os.path.join(config_global.root_path, config_run.dataset.sample_path, dir_name, config[0])
            dataset = TemplatesLINEMOD(
                root_dir=config_global.root_path,
                dataset=config[2],
                list_id_obj=config[3],
                split=config[1],
                image_size=config_run.dataset.image_size,
                save_path=save_sample_path,
                use_normalize=config_run.dataset.get('use_normalize', True)
            )
        else:  # LINEMOD or occlusionLINEMOD
            save_sample_path = os.path.join(config_global.root_path, config_run.dataset.sample_path, dir_name, config[0])
            dataset = LINEMODQuery(
                root_dir=config_global.root_path,
                dataset=config[2],
                list_id_obj=config[3],
                split=config[1],
                image_size=config_run.dataset.image_size,
                save_path=save_sample_path,
                use_normalize=config_run.dataset.get('use_normalize', True),
                use_mask=config_run.dataset.get('use_mask', False),
                gt_bbox_known=config_run.dataset.get('gt_bbox_known', True),
                use_aug=config_run.dataset.get('use_aug', False),
                aug_config=config_run.dataset.get('aug', None),
                num_negatives=config_run.dataset.get('num_negatives', 1)
            )
        datasetLoader[config[0]] = dataset
        print("---" * 20)

    # 使用init_dataloader初始化所有数据加载器
    datasetLoader = init_dataloader(
        dict_dataloader=datasetLoader,
        batch_size=config_run.train.batch_size,
        num_workers=config_run.train.num_workers
    )

    # 初始化优化器
    optimizer = torch.optim.Adam(
        list(model.parameters()),
        lr=config_run.train.optimizer.lr,
        weight_decay=config_run.train.optimizer.weight_decay)
    scores = metrics.init_score()

    # 初始化wandb
    if args.use_wandb:
        wandb.init(
            project="pose-estimation-423",
            name=wandb_name,
            config={
                "learning_rate": config_run.train.optimizer.lr,
                "batch_size": config_run.train.batch_size,
                "epochs": config_run.train.epochs,
                "weight_decay": config_run.train.optimizer.weight_decay,
                "descriptor_size": config_run.model.descriptor_size,
                "use_residual": config_run.model.get('use_residual', True),
                "use_pose_tokens": config_run.model.pose_tokens.get('use_attention', False),
                "split": config_run.dataset.split
            }
        )

    # 在训练开始前打印可训练参数数量
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"可训练参数数量: {trainable_params}")

    # 训练循环
    for epoch in tqdm(range(0, config_run.train.epochs)):
        # 更新学习率
        if epoch in config_run.train.scheduler.milestones:
            adjust_learning_rate(optimizer, config_run.train.optimizer.lr, config_run.train.scheduler.gamma)

        # 训练一个epoch
        train_loss = training_utils_dino.train(
            train_data=datasetLoader["train"],
            model=model,
            optimizer=optimizer,
            warm_up_config=[1000, config_run.train.optimizer.lr],
            epoch=epoch,
            logger=trainer_logger,
            log_interval=config_run.log.log_interval,
            regress_delta=config_run.model.regression_loss,
            use_wandb=args.use_wandb,
            config_run=config_run
        )

        # 每5个epoch进行测试
        if (epoch + 1) % 1 == 0:
            trainer_logger.info(f"Testing at epoch {epoch + 1}")

            # 进行测试
            for config_split in [["seen", seen_id_obj], ["unseen", unseen_id_obj]]:
                query_name = config_split[0] + "_test"
                template_name = config_split[0] + "_template"

                print(f"\nTesting {config_split[0]}...")
                testing_score = testing_utils_dino.test(
                    query_data=datasetLoader[query_name],
                    template_data=datasetLoader[template_name],
                    model=model,
                    split_name=config_split[0],
                    list_id_obj=config_split[1].tolist(),
                    epoch=epoch,
                    logger=trainer_logger,
                    vis=False,
                    result_vis_path=args.result_path,
                    tensor2im=tensor2im,
                    gt_bbox_known=config_run.dataset.gt_bbox_known
                )

                # 记录测试结果到日志
                trainer_logger.info(f"Epoch {epoch + 1} - {config_split[0]} test error: {testing_score['error, mean']:.4f}")
                trainer_logger.info(f"Epoch {epoch + 1} - {config_split[0]} test accuracy: {testing_score['accuracy, mean']:.4f}")

                # 记录测试结果到wandb
                if args.use_wandb:
                    wandb.log({
                        f"{config_split[0]}_test_error": testing_score['error, mean'],
                        f"{config_split[0]}_test_accuracy": testing_score['accuracy, mean'],
                        "epoch": epoch + 1
                    })

            # 保存模型
            save_file = os.path.join(save_path, 'model_epoch{}.pth'.format(epoch))
            trainer_logger.info("Saving to {}".format(save_file))
            os.makedirs(os.path.dirname(save_file), exist_ok=True)
            
            # 准备保存的状态字典
            state_dict = {
                'model': model.aggregation_network.state_dict()
            }
            
            # 如果使用了learnable tokens，也保存它们
            if model.dino_extractor.use_pose_tokens:
                state_dict['learnable_tokens'] = model.dino_extractor.learnable_tokens
                
            torch.save(state_dict, save_file)
        else:
            # 非测试epoch只记录训练损失
            trainer_logger.info(f'Epoch-{epoch}: train_loss={train_loss}')


if __name__ == "__main__":
    main()