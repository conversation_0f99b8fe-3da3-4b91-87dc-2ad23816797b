import argparse
import os
import torch
from tqdm import tqdm
import numpy as np

from lib.utils import weights, metrics, logger
from lib.utils.config import Config
from lib.datasets.dataloader_utils import init_dataloader

from lib.models.diffusion.stable_diffusion.resnet import collect_dims
from lib.models.diffusion.diffusion_extractor import DiffusionExtractor
from lib.models.diffusion.aggregation_network import AggregationNetwork
from lib.models.diffusion_network import DiffusionFeatureExtractor

from lib.datasets.linemod.dataloader_query_sam import LINEMODMultiQuery
from lib.datasets.linemod.dataloader_template import TemplatesLINEMOD
from lib.datasets.im_transform import im_transform as get_transform
from lib.datasets.linemod import inout
from lib.datasets.linemod import testing_utils_diffusion as testing_utils

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--split', type=str, choices=['split1', 'split2', 'split3'])
    parser.add_argument('--config_path', type=str)
    parser.add_argument('--checkpoint', type=str, required=True)
    args = parser.parse_args()

    config_global = Config(config_file="./config.json").get_config()
    config_run = Config(args.config_path).get_config()

    # 初始化全局配置
    dir_name = (args.config_path.split('/')[-1]).split('.')[0]
    print("config", dir_name)
    save_path = os.path.join(config_global.root_path, config_run.log.weights, dir_name)
    trainer_dir = os.path.join(os.getcwd(), "logs")
    trainer_logger = logger.init_logger(save_path=save_path,
                                      trainer_dir=trainer_dir,
                                      trainer_logger_name=dir_name)

    # 初始化模型
    print("Initializing model...")
    diffusion_extractor = DiffusionExtractor(config_run, "cuda")
    dims = collect_dims(diffusion_extractor.unet, idxs=diffusion_extractor.idxs)
    aggregation_network = AggregationNetwork(
        descriptor_size=config_run.model.descriptor_size,
        feature_dims=dims,
        device="cuda",
    )
    model = DiffusionFeatureExtractor(
        config=config_run,
        threshold=0.2,
        diffusion_extractor=diffusion_extractor,
        aggregation_network=aggregation_network,
    ).cuda()

    # 加载检查点
    print("Loading checkpoint from:", args.checkpoint)
    weights.load_checkpoint(model=model.aggregation_network, pth_path=args.checkpoint)
    model.eval()

    # 初始化transform
    print("Initializing transform...")
    transform = get_transform()

    # 创建数据加载器
    print("Creating dataloaders...")
    seen_id_obj, seen_names, seen_occ_id_obj, seen_occ_names, unseen_id_obj, unseen_names, \
        unseen_occ_id_obj, unseen_occ_names = inout.get_list_id_obj_from_split_name(config_run.dataset.split)
    
    config_loader = [
        ["seen_test", "seen_test", "sam_crops", seen_id_obj],
        ["unseen_test", "test", "sam_crops", unseen_id_obj],
        ["seen_template", "test", "templatesLINEMOD", seen_id_obj],
        ["unseen_template", "test", "templatesLINEMOD", unseen_id_obj],
        ["seen_occ_test", "test", "sam_crops_occ", seen_occ_id_obj],
        ["unseen_occ_test", "test", "sam_crops_occ", unseen_occ_id_obj],
        ["seen_occ_template", "test", "templatesLINEMOD", seen_occ_id_obj],
        ["unseen_occ_template", "test", "templatesLINEMOD", unseen_occ_id_obj]
    ]

    datasetLoader = {}
    for config in config_loader:
        print("Dataset", config[0], config[2], config[3])
        if config[2] == "templatesLINEMOD":
            save_sample_path = os.path.join(config_global.root_path, config_run.dataset.sample_path, dir_name, config[0])
            loader = TemplatesLINEMOD(root_dir=config_global.root_path, dataset=config[2], list_id_obj=config[3],
                                    split=config[1], image_size=config_run.dataset.image_size,
                                    mask_size=config_run.dataset.mask_size, im_transform=transform,
                                    save_path=save_sample_path)
        elif config[2] in ["sam_crops", "sam_crops_occ"]:
            # 对于SAM裁剪的图像，直接使用sam_crops或sam_crops_occ目录
            save_sample_path = os.path.join(config_global.root_path, config[2])
            loader = LINEMODMultiQuery(
                root_dir=config_global.root_path,
                dataset=config[2],
                list_id_obj=config[3],
                split=config[1],
                image_size=config_run.dataset.image_size,
                mask_size=config_run.dataset.mask_size,
                im_transform=transform,
                save_path=save_sample_path
            )
        datasetLoader[config[0]] = loader
        print("---" * 20)

    datasetLoader = init_dataloader(dict_dataloader=datasetLoader,
                                  batch_size=config_run.train.batch_size,
                                  num_workers=config_run.train.num_workers)

    # 测试
    print("Starting testing...")
    new_score = {}
    with torch.no_grad():
        for config_split in [["seen", seen_id_obj], ["seen_occ", seen_occ_id_obj],
                           ["unseen", unseen_id_obj], ["unseen_occ", unseen_occ_id_obj]]:
            query_name = config_split[0] + "_test"
            template_name = config_split[0] + "_template"
            
            print(f"\nTesting {config_split[0]}...")
            err, acc, recog, class_and_pose = testing_utils.test(
                query_data=datasetLoader[query_name],
                template_data=datasetLoader[template_name],
                model=model,
                split_name=config_split[0],
                list_id_obj=config_split[1].tolist(),
                epoch=-1,
                logger=trainer_logger,
                use_target_crop=True
            )
            
            # 保存分数
            new_score[config_split[0] + "_err"] = err
            new_score[config_split[0] + "_acc"] = acc
            new_score[config_split[0] + "_recog"] = recog
            new_score[config_split[0] + "_class_and_pose"] = class_and_pose

    # 打印最终分数
    print("\nFinal Scores:")
    print(new_score)

if __name__ == "__main__":
    main() 