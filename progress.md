# 项目修改进度记录

## 2024-01-xx
### 初始化
- 创建progress.md文件用于记录项目修改进度
- 准备对数据集处理和读取部分进行重构

### 已完成
- [x] 创建新的bop数据加载模块的基本结构
- [x] 实现BOP数据集的基础IO功能（inout.py）
- [x] 实现新的query数据加载器（dataloader_query.py）
- [x] 实现新的template数据加载器（dataloader_template.py）

### 主要更改
1. 创建了新的BOP数据集加载模块
   - 支持从BOP格式数据集读取图像ID和位姿信息
   - 与sam_crops文件夹集成，用于加载裁剪后的图像
   - 保持与原有代码接口的兼容性

2. 数据加载器的改进
   - Query加载器：从BOP数据集读取场景信息，关联sam_crops中的裁剪图像
   - Template加载器：使用预定义位姿生成模板，从sam_crops中加载对应图像

### 待完成任务
- [ ] 添加数据预处理和增强功能
- [ ] 测试数据加载功能
- [ ] 添加详细的使用文档

### 注意事项
- 主要关注数据集处理和读取部分的修改
- 需要保持与原有sam_crops的兼容性
- 遵循BOP数据集格式规范 