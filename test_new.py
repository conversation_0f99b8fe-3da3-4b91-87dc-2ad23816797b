import argparse
import os
import torch
from tqdm import tqdm
import numpy as np
from tabulate import tabulate

from lib.utils import weights, metrics, logger
from lib.utils.config import Config
from lib.datasets.dataloader_utils import init_dataloader

from lib.models.new_dino_network import NewDINOExtractor

from lib.datasets.linemod.dataloader_dino import LINEMODQuery, TemplatesLINEMOD
from lib.datasets.im_transform import im_transform, tensor2im
from lib.datasets.linemod import inout
from lib.datasets.linemod import testing_utils_dino as testing_utils

import os
os.environ['CUDA_VISIBLE_DEVICES'] = '5'

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--split', type=str, choices=['split1', 'split2', 'split3'])
    parser.add_argument('--config_path', type=str, default='config_run/LM_DINO_split1.json')
    # parser.add_argument('--checkpoint', type=str, default='/home/<USER>/projects/RJL2025/my-diff-feats-pose/dataset/results/weights/new_LM_DINO_split1_20250707_104016/model_epoch5.pth')
    parser.add_argument('--checkpoint', type=str)
    parser.add_argument('--result_path', type=str, default='./dataset/results/vis/linemod')
    args = parser.parse_args()

    config_global = Config(config_file="./config.json").get_config()
    config_run = Config(args.config_path).get_config()

    args.result_path = os.path.join(args.result_path, config_run.dataset.split)
    if not os.path.exists(args.result_path):
        os.makedirs(args.result_path)

    # initialize global config for testing
    dir_name = (args.config_path.split('/')[-1]).split('.')[0]
    print("config", dir_name)
    save_path = os.path.join(config_global.root_path, config_run.log.weights, dir_name)
    trainer_dir = os.getcwd()
    trainer_logger = logger.init_logger(save_path=save_path,
                                      trainer_dir=trainer_dir,
                                      trainer_logger_name=dir_name)

    # initialize model
    print("Initializing model...")
    
    # 先创建DINO extractor
    dino_extractor = NewDINOExtractor(
        device="cuda",
        feature_blocks_config=config_run.model.feature_blocks,
        version=config_run.model.version,
        input_size=config_run.model.input_size
    )
    
    # 使用智能工厂函数自动选择合适的特征提取器
    from lib.models.simple_dino_feature_network import create_feature_extractor, OffsetPredictor
    from lib.models.learnable_hash import HashEncoder
    model = create_feature_extractor(
        config=config_run,
        dino_extractor=dino_extractor,
    ).cuda()

    # 检查是否启用偏移量预测，如果是则创建独立的偏移量预测器
    offset_predictor = None
    offset_enabled = getattr(config_run.model, 'offset_predictor', {}).get('enabled', False)
    if offset_enabled:
        print("🔧 Creating offset predictor for testing...")
        offset_predictor = OffsetPredictor(config_run)

    # 检查是否启用Hash功能，如果是则创建独立的Hash编码器
    hash_encoder = None
    hash_enabled = getattr(config_run.model, 'learnable_hash', {}).get('enabled', False)
    if hash_enabled:
        print("🔧 Creating hash encoder for testing...")
        hash_encoder = HashEncoder(config_run)

    # load checkpoint
    if args.checkpoint:
        print("Loading checkpoint from:", args.checkpoint)
        checkpoint = torch.load(args.checkpoint)
        if 'model' in checkpoint:
            model.load_state_dict(checkpoint['model'])
            # 如果有offset predictor，也加载其权重
            if offset_predictor is not None:
                offset_predictor.load_state_dict(checkpoint['model'])
            # 如果有hash encoder，也加载其权重
            if hash_encoder is not None:
                hash_encoder.load_state_dict(checkpoint['model'])
        elif 'aggregation_network' in checkpoint:
            # 兼容原始版本的checkpoint格式
            model.aggregation_network.load_state_dict(checkpoint['aggregation_network'])
        elif 'state_dict' in checkpoint:
            model.load_state_dict(checkpoint['state_dict'])
            # 如果有offset predictor，也加载其权重
            if offset_predictor is not None:
                offset_predictor.load_state_dict(checkpoint['state_dict'])
            # 如果有hash encoder，也加载其权重
            if hash_encoder is not None:
                hash_encoder.load_state_dict(checkpoint['state_dict'])
        else:
            model.load_state_dict(checkpoint)
            # 如果有offset predictor，也加载其权重
            if offset_predictor is not None:
                offset_predictor.load_state_dict(checkpoint)
    else:
        print("No checkpoint provided, using randomly initialized model")
    
    model.eval()

    # initialize transform
    print("Initializing transform...")
    # im_transform = im_transform()
    # tensor2im = tensor2im

    # create dataloaders
    print("Creating dataloaders...")
    seen_id_obj, seen_names, seen_occ_id_obj, seen_occ_names, unseen_id_obj, unseen_names, \
        unseen_occ_id_obj, unseen_occ_names = inout.get_list_id_obj_from_split_name(config_run.dataset.split)

    config_loader = [
        ["seen_test", "seen_test", "LINEMOD", seen_id_obj],
        ["unseen_test", "test", "LINEMOD", unseen_id_obj],
        ["seen_template", "test", "templatesLINEMOD", seen_id_obj],
        ["unseen_template", "test", "templatesLINEMOD", unseen_id_obj],
        ["seen_occ_test", "test", "occlusionLINEMOD", seen_occ_id_obj],
        ["unseen_occ_test", "test", "occlusionLINEMOD", unseen_occ_id_obj],
        ["seen_occ_template", "test", "templatesLINEMOD", seen_occ_id_obj],
        ["unseen_occ_template", "test", "templatesLINEMOD", unseen_occ_id_obj]
    ]

    # 检查是否启用CAD特征
    use_cad_feature = config_run.model.cad_feature.get('enabled', False)
    print(f"CAD feature enabled: {use_cad_feature}")

    datasetLoader = {}
    for config in config_loader:
        print("Dataset", config[0], config[2], config[3])
        if config[2] == "templatesLINEMOD":
            save_sample_path = os.path.join(config_global.root_path, config_run.dataset.sample_path, dir_name, config[0])
            loader = TemplatesLINEMOD(
                root_dir=config_global.root_path,
                dataset=config[2],
                list_id_obj=config[3],
                split=config[1],
                image_size=config_run.dataset.image_size,
                save_path=save_sample_path,
                use_normalize=config_run.dataset.get('use_normalize', True),
                use_cad_feature=use_cad_feature
            )
        else:  # LINEMOD or occlusionLINEMOD
            save_sample_path = os.path.join(config_global.root_path, config_run.dataset.sample_path, dir_name, config[0])
            loader = LINEMODQuery(
                root_dir=config_global.root_path,
                dataset=config[2],
                list_id_obj=config[3],
                split=config[1],
                image_size=config_run.dataset.image_size,
                save_path=save_sample_path,
                use_normalize=config_run.dataset.get('use_normalize', True),
                use_mask=config_run.dataset.get('use_mask', False),
                gt_bbox_known=config_run.dataset.get('gt_bbox_known', True),
                use_aug=config_run.dataset.get('use_aug', False),
                aug_config=config_run.dataset.get('aug', None),
                num_negatives=config_run.dataset.get('num_negatives', 1),
                use_cad_feature=use_cad_feature
            )
        datasetLoader[config[0]] = loader
        print("---" * 20)

    datasetLoader = init_dataloader(dict_dataloader=datasetLoader,
                                  batch_size=config_run.train.batch_size,
                                  num_workers=config_run.train.num_workers)


    # testing
    print("Starting testing...")

    # 确定测试模式：根据配置决定是否需要测试两种模式
    config_gt_bbox_known = config_run.dataset.gt_bbox_known
    if config_gt_bbox_known:
        # 配置为True：只测试gt_bbox_known=True模式
        test_modes = [("known", True)]
        print("📋 测试模式：仅测试 gt_bbox_known=True (已知边界框)")
    else:
        # 配置为False：测试两种模式
        test_modes = [("known", True), ("unknown", False)]
        print("📋 测试模式：测试 gt_bbox_known=True 和 gt_bbox_known=False (已知+未知边界框)")

    new_score = {}
    with torch.no_grad():
        for config_split in [["seen",seen_id_obj], ["unseen", unseen_id_obj], ["seen_occ", seen_occ_id_obj], ["unseen_occ", unseen_occ_id_obj]]:
        # for config_split in [["unseen_occ", unseen_occ_id_obj]]:
            query_name = config_split[0] + "_test"
            template_name = config_split[0] + "_template"

            # 对每个数据集，根据test_modes进行测试
            for mode_name, gt_bbox_known in test_modes:
                print(f"\n🔍 Testing {config_split[0]} - {mode_name} mode (gt_bbox_known={gt_bbox_known})...")

                # 为不同模式创建独立的结果保存路径
                mode_result_path = os.path.join(args.result_path, mode_name)
                os.makedirs(mode_result_path, exist_ok=True)

                testing_score = testing_utils.test(
                    query_data=datasetLoader[query_name],
                    template_data=datasetLoader[template_name],
                    model=model,
                    split_name=config_split[0],
                    list_id_obj=config_split[1].tolist(),
                    epoch=-1,
                    logger=trainer_logger,
                    vis=False,
                    result_vis_path=mode_result_path,  # 使用模式特定的路径
                    tensor2im=tensor2im,
                    gt_bbox_known=gt_bbox_known,  # 使用当前测试模式的设置
                    offset_predictor=offset_predictor,  # 传递独立的offset predictor
                    hash_encoder=hash_encoder           # 传递hash encoder
                )

                # 保存分数 - 为不同模式添加前缀
                if len(test_modes) > 1:
                    # 双模式：添加模式前缀
                    for key, value in testing_score.items():
                        new_score[f"{mode_name}_{key}"] = value
                else:
                    # 单模式：保持原有格式
                    new_score.update(testing_score)
            
                # 显示当前模式的结果
                print(f"\n{config_split[0].upper()} - {mode_name.upper()} MODE Results:")
                print("=" * 60)

                # 准备表格数据 - 添加新的精度指标
                metrics = ["error", "accuracy5", "accuracy10", "accuracy15", "recognition",
                          "recognition and pose5", "recognition and pose10", "recognition and pose15"]
                headers = ["Object"] + metrics
                table_data = []

                # 添加mean行
                mean_row = ["Mean"]
                for metric in metrics:
                    key = f"{metric}, mean"
                    mean_row.append(f"{testing_score[key]:.4f}" if key in testing_score else "N/A")
                table_data.append(mean_row)

                # 添加每个物体的行
                for id_obj in config_split[1]:
                    obj_name = inout.LINEMOD_real_id_to_name[id_obj]
                    row = [obj_name]
                    for metric in metrics:
                        key = f"{metric}, {obj_name}"
                        row.append(f"{testing_score[key]:.4f}" if key in testing_score else "N/A")
                    table_data.append(row)

                # 打印表格
                print(tabulate(table_data, headers=headers, tablefmt="grid", floatfmt=".4f"))
                print("\n" + "=" * 60)

    # 保存最终结果
    print("\n" + "=" * 80)
    print("FINAL SUMMARY")
    print("=" * 80)

    # 计算总体平均分数
    all_splits = ["seen", "unseen", "seen_occ", "unseen_occ"]

    if len(test_modes) > 1:
        # 双模式：分别显示两种模式的总结
        for mode_name, _ in test_modes:
            print(f"\n📊 {mode_name.upper()} MODE Overall Results:")
            print("-" * 50)

            overall_metrics = {}
            for metric in ["error", "accuracy5", "accuracy10", "accuracy15", "recognition",
                           "recognition and pose5", "recognition and pose10", "recognition and pose15"]:
                values = []
                for split in all_splits:
                    key = f"{mode_name}_{metric}, mean"
                    if key in new_score:
                        values.append(new_score[key])
                if values:
                    overall_metrics[metric] = np.mean(values)

            for metric, value in overall_metrics.items():
                print(f"{metric.capitalize()}: {value:.4f}")

        # 比较两种模式的关键指标
        print(f"\n🔄 MODE COMPARISON:")
        print("-" * 50)
        key_metrics = ["accuracy15", "recognition and pose15"]
        for metric in key_metrics:
            known_values = []
            unknown_values = []
            for split in all_splits:
                known_key = f"known_{metric}, mean"
                unknown_key = f"unknown_{metric}, mean"
                if known_key in new_score:
                    known_values.append(new_score[known_key])
                if unknown_key in new_score:
                    unknown_values.append(new_score[unknown_key])

            if known_values and unknown_values:
                known_avg = np.mean(known_values)
                unknown_avg = np.mean(unknown_values)
                diff = known_avg - unknown_avg
                print(f"{metric.capitalize()}:")
                print(f"  Known bbox: {known_avg:.4f}")
                print(f"  Unknown bbox: {unknown_avg:.4f}")
                print(f"  Difference: {diff:.4f} ({'↑' if diff > 0 else '↓'})")
    else:
        # 单模式：保持原有显示方式
        overall_metrics = {}
        for metric in ["error", "accuracy5", "accuracy10", "accuracy15", "recognition",
                       "recognition and pose5", "recognition and pose10", "recognition and pose15"]:
            values = []
            for split in all_splits:
                key = f"{metric}, mean"
                if key in new_score:
                    values.append(new_score[key])
            if values:
                overall_metrics[metric] = np.mean(values)

        print("Overall Average Results:")
        for metric, value in overall_metrics.items():
            print(f"{metric.capitalize()}: {value:.4f}")

    print(f"\n✅ Testing completed successfully!")
    print(f"📁 Results saved to: {args.result_path}")
    if len(test_modes) > 1:
        print(f"   ├── known/ (gt_bbox_known=True results)")
        print(f"   └── unknown/ (gt_bbox_known=False results)")

if __name__ == "__main__":
    main() 