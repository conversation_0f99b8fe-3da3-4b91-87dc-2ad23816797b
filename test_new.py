import argparse
import os
import torch
from tqdm import tqdm
import numpy as np
from tabulate import tabulate

from lib.utils import weights, metrics, logger
from lib.utils.config import Config
from lib.datasets.dataloader_utils import init_dataloader

from lib.models.new_dino_network import NewDINOExtractor

from lib.datasets.linemod.dataloader_dino import LINEMODQuery, TemplatesLINEMOD
from lib.datasets.im_transform import im_transform, tensor2im
from lib.datasets.linemod import inout
from lib.datasets.linemod import testing_utils_dino as testing_utils

import os
os.environ['CUDA_VISIBLE_DEVICES'] = '5'

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--split', type=str, choices=['split1', 'split2', 'split3'])
    parser.add_argument('--config_path', type=str, default='config_run/LM_DINO_split1.json')
    # parser.add_argument('--checkpoint', type=str, default='/home/<USER>/projects/RJL2025/my-diff-feats-pose/dataset/results/weights/new_LM_DINO_split1_20250707_104016/model_epoch5.pth')
    parser.add_argument('--checkpoint', type=str)
    parser.add_argument('--result_path', type=str, default='./dataset/results/vis/linemod')
    args = parser.parse_args()

    config_global = Config(config_file="./config.json").get_config()
    config_run = Config(args.config_path).get_config()

    args.result_path = os.path.join(args.result_path, config_run.dataset.split)
    if not os.path.exists(args.result_path):
        os.makedirs(args.result_path)

    # initialize global config for testing
    dir_name = (args.config_path.split('/')[-1]).split('.')[0]
    print("config", dir_name)
    save_path = os.path.join(config_global.root_path, config_run.log.weights, dir_name)
    trainer_dir = os.getcwd()
    trainer_logger = logger.init_logger(save_path=save_path,
                                      trainer_dir=trainer_dir,
                                      trainer_logger_name=dir_name)

    # initialize model
    print("Initializing model...")
    
    # 先创建DINO extractor
    dino_extractor = NewDINOExtractor(
        device="cuda",
        feature_blocks_config=config_run.model.feature_blocks,
        version=config_run.model.version,
        input_size=config_run.model.input_size
    )
    
    # 使用智能工厂函数自动选择合适的特征提取器
    from lib.models.simple_dino_feature_network import create_feature_extractor, OffsetPredictor
    from lib.models.learnable_hash import HashEncoder
    model = create_feature_extractor(
        config=config_run,
        dino_extractor=dino_extractor,
    ).cuda()

    # 检查是否启用偏移量预测，如果是则创建独立的偏移量预测器
    offset_predictor = None
    offset_enabled = getattr(config_run.model, 'offset_predictor', {}).get('enabled', False)
    if offset_enabled:
        print("🔧 Creating offset predictor for testing...")
        offset_predictor = OffsetPredictor(config_run)

    # 检查是否启用Hash功能，如果是则创建独立的Hash编码器
    hash_encoder = None
    hash_enabled = getattr(config_run.model, 'learnable_hash', {}).get('enabled', False)
    if hash_enabled:
        print("🔧 Creating hash encoder for testing...")
        hash_encoder = HashEncoder(config_run)

    # load checkpoint
    if args.checkpoint:
        print("Loading checkpoint from:", args.checkpoint)
        checkpoint = torch.load(args.checkpoint)
        if 'model' in checkpoint:
            model.load_state_dict(checkpoint['model'])
            # 如果有offset predictor，也加载其权重
            if offset_predictor is not None:
                offset_predictor.load_state_dict(checkpoint['model'])
            # 如果有hash encoder，也加载其权重
            if hash_encoder is not None:
                hash_encoder.load_state_dict(checkpoint['model'])
        elif 'aggregation_network' in checkpoint:
            # 兼容原始版本的checkpoint格式
            model.aggregation_network.load_state_dict(checkpoint['aggregation_network'])
        elif 'state_dict' in checkpoint:
            model.load_state_dict(checkpoint['state_dict'])
            # 如果有offset predictor，也加载其权重
            if offset_predictor is not None:
                offset_predictor.load_state_dict(checkpoint['state_dict'])
            # 如果有hash encoder，也加载其权重
            if hash_encoder is not None:
                hash_encoder.load_state_dict(checkpoint['state_dict'])
        else:
            model.load_state_dict(checkpoint)
            # 如果有offset predictor，也加载其权重
            if offset_predictor is not None:
                offset_predictor.load_state_dict(checkpoint)
    else:
        print("No checkpoint provided, using randomly initialized model")
    
    model.eval()

    # initialize transform
    print("Initializing transform...")
    # im_transform = im_transform()
    # tensor2im = tensor2im

    # create dataloaders
    print("Creating dataloaders...")
    seen_id_obj, seen_names, seen_occ_id_obj, seen_occ_names, unseen_id_obj, unseen_names, \
        unseen_occ_id_obj, unseen_occ_names = inout.get_list_id_obj_from_split_name(config_run.dataset.split)

    config_loader = [
        ["seen_test", "seen_test", "LINEMOD", seen_id_obj],
        ["unseen_test", "test", "LINEMOD", unseen_id_obj],
        ["seen_template", "test", "templatesLINEMOD", seen_id_obj],
        ["unseen_template", "test", "templatesLINEMOD", unseen_id_obj],
        ["seen_occ_test", "test", "occlusionLINEMOD", seen_occ_id_obj],
        ["unseen_occ_test", "test", "occlusionLINEMOD", unseen_occ_id_obj],
        ["seen_occ_template", "test", "templatesLINEMOD", seen_occ_id_obj],
        ["unseen_occ_template", "test", "templatesLINEMOD", unseen_occ_id_obj]
    ]

    # 检查是否启用CAD特征
    use_cad_feature = config_run.model.cad_feature.get('enabled', False)
    print(f"CAD feature enabled: {use_cad_feature}")

    datasetLoader = {}
    for config in config_loader:
        print("Dataset", config[0], config[2], config[3])
        if config[2] == "templatesLINEMOD":
            save_sample_path = os.path.join(config_global.root_path, config_run.dataset.sample_path, dir_name, config[0])
            loader = TemplatesLINEMOD(
                root_dir=config_global.root_path,
                dataset=config[2],
                list_id_obj=config[3],
                split=config[1],
                image_size=config_run.dataset.image_size,
                save_path=save_sample_path,
                use_normalize=config_run.dataset.get('use_normalize', True),
                use_cad_feature=use_cad_feature
            )
        else:  # LINEMOD or occlusionLINEMOD
            save_sample_path = os.path.join(config_global.root_path, config_run.dataset.sample_path, dir_name, config[0])
            loader = LINEMODQuery(
                root_dir=config_global.root_path,
                dataset=config[2],
                list_id_obj=config[3],
                split=config[1],
                image_size=config_run.dataset.image_size,
                save_path=save_sample_path,
                use_normalize=config_run.dataset.get('use_normalize', True),
                use_mask=config_run.dataset.get('use_mask', False),
                gt_bbox_known=config_run.dataset.get('gt_bbox_known', True),
                use_aug=config_run.dataset.get('use_aug', False),
                aug_config=config_run.dataset.get('aug', None),
                num_negatives=config_run.dataset.get('num_negatives', 1),
                use_cad_feature=use_cad_feature
            )
        datasetLoader[config[0]] = loader
        print("---" * 20)

    datasetLoader = init_dataloader(dict_dataloader=datasetLoader,
                                  batch_size=config_run.train.batch_size,
                                  num_workers=config_run.train.num_workers)


    # testing
    print("Starting testing...")
    new_score = {}
    with torch.no_grad():
        for config_split in [["seen",seen_id_obj], ["unseen", unseen_id_obj], ["seen_occ", seen_occ_id_obj], ["unseen_occ", unseen_occ_id_obj]]:
        # for config_split in [["unseen_occ", unseen_occ_id_obj]]:
            query_name = config_split[0] + "_test"
            template_name = config_split[0] + "_template"
            print(f"\nTesting {config_split[0]}...")
            testing_score = testing_utils.test(
                query_data=datasetLoader[query_name],
                template_data=datasetLoader[template_name],
                model=model,
                split_name=config_split[0],
                list_id_obj=config_split[1].tolist(),
                epoch=-1,
                logger=trainer_logger,
                vis=False,
                result_vis_path=args.result_path,
                tensor2im=tensor2im,
                gt_bbox_known=config_run.dataset.gt_bbox_known,
                offset_predictor=offset_predictor,  # 传递独立的offset predictor
                hash_encoder=hash_encoder           # 传递hash encoder
            )

            # 保存分数
            new_score.update(testing_score)
            
            print(f"\n{config_split[0].upper()} Results:")
            print("=" * 50)
            
            # 准备表格数据 - 添加新的精度指标
            metrics = ["error", "accuracy5", "accuracy10", "accuracy15", "recognition",
                      "recognition and pose5", "recognition and pose10", "recognition and pose15"]
            headers = ["Object"] + metrics
            table_data = []

            # 添加mean行
            mean_row = ["Mean"]
            for metric in metrics:
                key = f"{metric}, mean"
                mean_row.append(f"{testing_score[key]:.4f}" if key in testing_score else "N/A")
            table_data.append(mean_row)

            # 添加每个物体的行
            for id_obj in config_split[1]:
                obj_name = inout.LINEMOD_real_id_to_name[id_obj]
                row = [obj_name]
                for metric in metrics:
                    key = f"{metric}, {obj_name}"
                    row.append(f"{testing_score[key]:.4f}" if key in testing_score else "N/A")
                table_data.append(row)

            # 打印表格
            print(tabulate(table_data, headers=headers, tablefmt="grid", floatfmt=".4f"))
            print("\n" + "=" * 50)

    # 保存最终结果
    print("\n" + "=" * 60)
    print("FINAL SUMMARY")
    print("=" * 60)
    
    # 计算总体平均分数
    all_splits = ["seen", "unseen", "seen_occ", "unseen_occ"]
    overall_metrics = {}
    
    for metric in ["error", "accuracy5", "accuracy10", "accuracy15", "recognition",
                   "recognition and pose5", "recognition and pose10", "recognition and pose15"]:
        values = []
        for split in all_splits:
            key = f"{metric}, mean"
            if key in new_score:
                values.append(new_score[key])
        if values:
            overall_metrics[metric] = np.mean(values)
    
    print("Overall Average Results:")
    for metric, value in overall_metrics.items():
        print(f"{metric.capitalize()}: {value:.4f}")
    
    print(f"\nTesting completed successfully!")
    print(f"Results saved to: {args.result_path}")

if __name__ == "__main__":
    main() 