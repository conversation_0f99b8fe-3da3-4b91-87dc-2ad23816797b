特征提取器可训练参数数量: 3.21M
❌ 偏移量预测功能未启用
Hash编码器可训练参数数量: 0.13M
✅ Hash编码功能已启用
   📅 Hash训练将从epoch 0开始
   📊 Hash阶段优化器参数数量: 0.13M
初始阶段优化器参数数量: 3.21M
100%|████████████████████████████████████████████| 554/554 [54:37<00:00,  5.92s/it]

🔄 Epoch 0: 切换到Hash训练阶段（实验1：简化版本）
   🎯 目标：复现wrap版本的训练方式
   📝 配置：只使用Hash特征损失 + Hash正则化损失
   📊 简化训练参数统计:
      - 主要模型: 3.21M
      - Hash编码器: 0.13M
      - 总计: 3.35M
   📈 统一学习率: 0.0001
   ✅ 优化器已切换到简化Hash训练模式（等价wrap版本）
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|██████████████████████████████| 151/151 [01:05<00:00,  2.30it/s]1s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
🔍 检查梯度传播:
   Model有梯度: True
   Hash pose编码器有梯度: True
   Hash cls编码器有梯度: True

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|██████████████████████████████| 872/872 [00:55<00:00, 15.61it/s]
处理模板数据: 100%|████████████████████████████████| 95/95 [00:41<00:00,  2.30it/s]
Epoch-0, seen -- Mean err: 4.29, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 0: 2.07 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 6061/6061 [06:18<00:00, 16.03it/s]
处理模板数据: 100%|██████████████████████████████| 113/113 [00:48<00:00,  2.32it/s]
Epoch-0, unseen -- Mean err: 5.61, Acc: 0.98, Rec : 1.00, Class and Pose  : 0.98
Validation time for epoch 0: 7.03 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 6649/6649 [06:56<00:00, 15.98it/s]
处理模板数据: 100%|████████████████████████████████| 38/38 [00:17<00:00,  2.23it/s]
Epoch-0, seen_occ -- Mean err: 12.54, Acc: 0.84, Rec : 0.90, Class and Pose  : 0.84
Validation time for epoch 0: 7.79 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 1688/1688 [01:15<00:00, 22.41it/s]
100%|████████████████████████████████████████████| 554/554 [54:00<00:00,  5.85s/it]
Epoch-0, unseen_occ -- Mean err: 15.35, Acc: 0.77, Rec : 1.00, Class and Pose  : 0.77
Validation time for epoch 0: 1.58 minutes
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|██████████████████████████████| 151/151 [01:05<00:00,  2.31it/s]5s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
🔍 检查梯度传播:
   Model有梯度: True
   Hash pose编码器有梯度: True
   Hash cls编码器有梯度: True

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|██████████████████████████████| 872/872 [00:45<00:00, 19.36it/s]
处理模板数据: 100%|████████████████████████████████| 95/95 [00:40<00:00,  2.32it/s]
Epoch-1, seen -- Mean err: 4.41, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 1: 1.88 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 6061/6061 [04:52<00:00, 20.72it/s]
处理模板数据: 100%|██████████████████████████████| 113/113 [00:48<00:00,  2.31it/s]
Epoch-1, unseen -- Mean err: 5.54, Acc: 0.98, Rec : 1.00, Class and Pose  : 0.98
Validation time for epoch 1: 5.60 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 6649/6649 [05:18<00:00, 20.86it/s]
处理模板数据: 100%|████████████████████████████████| 38/38 [00:16<00:00,  2.26it/s]
Epoch-1, seen_occ -- Mean err: 13.48, Acc: 0.83, Rec : 0.90, Class and Pose  : 0.83
Validation time for epoch 1: 6.16 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 1688/1688 [01:16<00:00, 21.98it/s]
  8%|███▏                                    | 2/25 [2:22:30<27:11:06, 4255.06s/it]
Epoch-1, unseen_occ -- Mean err: 14.89, Acc: 0.78, Rec : 0.99, Class and Pose  : 0.78
Validation time for epoch 1: 1.59 minutes
✅ Hash编码器已设置为训练模式
  3%|█▏                                           | 14/554 [01:33<52:22,  5.82s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
🔍 检查梯度传播:
   Model有梯度: True
   Hash pose编码器有梯度: True
   Hash cls编码器有梯度: True
