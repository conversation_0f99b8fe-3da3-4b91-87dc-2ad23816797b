特征提取器可训练参数数量: 1.43M
❌ 偏移量预测功能未启用
❌ Hash编码功能未启用
初始阶段优化器参数数量: 1.43M
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 554/554 [13:38<00:00,  1.48s/it]
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 151/151 [00:15<00:00,  9.51it/s]0s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
📋 测试模式：测试 gt_bbox_known=True 和 gt_bbox_known=False (已知+未知边界框)

🔍 Testing seen - known mode (gt_bbox_known=True)...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 872/872 [00:49<00:00, 17.62it/s]
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 151/151 [00:16<00:00,  9.26it/s]
Epoch-0, seen -- Mean err: 4.21, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
详细精度 - Acc5: 0.70, Acc10: 0.98, Acc15: 1.00
类别+姿态 - Acc5: 0.70, Acc10: 0.98, Acc15: 1.00
Validation time for epoch 0: 1.13 minutes

🔍 Testing seen - unknown mode (gt_bbox_known=False)...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 872/872 [01:23<00:00, 10.50it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 95/95 [00:10<00:00,  9.16it/s]
Epoch-0, seen -- Mean err: 4.20, Acc: 1.00, Rec : 0.95, Class and Pose  : 0.94
详细精度 - Acc5: 0.70, Acc10: 0.98, Acc15: 1.00
类别+姿态 - Acc5: 0.66, Acc10: 0.92, Acc15: 0.94
Validation time for epoch 0: 1.69 minutes

🔍 Testing unseen - known mode (gt_bbox_known=True)...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 6061/6061 [06:13<00:00, 16.22it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 95/95 [00:10<00:00,  9.31it/s]
Epoch-0, unseen -- Mean err: 5.24, Acc: 0.98, Rec : 1.00, Class and Pose  : 0.98
详细精度 - Acc5: 0.57, Acc10: 0.93, Acc15: 0.98
类别+姿态 - Acc5: 0.57, Acc10: 0.93, Acc15: 0.98
Validation time for epoch 0: 6.43 minutes

🔍 Testing unseen - unknown mode (gt_bbox_known=False)...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 6061/6061 [09:35<00:00, 10.53it/s]
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 113/113 [00:12<00:00,  9.22it/s]
Epoch-0, unseen -- Mean err: 5.43, Acc: 0.98, Rec : 0.72, Class and Pose  : 0.70
详细精度 - Acc5: 0.55, Acc10: 0.92, Acc15: 0.98
类别+姿态 - Acc5: 0.40, Acc10: 0.66, Acc15: 0.70
Validation time for epoch 0: 9.80 minutes

🔍 Testing seen_occ - known mode (gt_bbox_known=True)...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 6649/6649 [05:38<00:00, 19.66it/s]
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 113/113 [00:12<00:00,  9.33it/s]
Epoch-0, seen_occ -- Mean err: 12.22, Acc: 0.85, Rec : 0.92, Class and Pose  : 0.84
详细精度 - Acc5: 0.44, Acc10: 0.78, Acc15: 0.85
类别+姿态 - Acc5: 0.44, Acc10: 0.78, Acc15: 0.84
Validation time for epoch 0: 5.87 minutes

🔍 Testing seen_occ - unknown mode (gt_bbox_known=False)...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 113/113 [00:11<00:00, 10.14it/s]
处理查询图像:  59%|██████████████████████████████████████████████████████████████████▊                                              | 3932/6649 [06:08<04:11, 10.80it/s]
