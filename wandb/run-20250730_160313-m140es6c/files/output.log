特征提取器可训练参数数量: 3.21M
❌ 偏移量预测功能未启用
❌ Hash编码功能未启用
初始阶段优化器参数数量: 3.21M
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 554/554 [54:10<00:00,  5.87s/it]
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 151/151 [01:06<00:00,  2.28it/s]6s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 872/872 [00:56<00:00, 15.37it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 95/95 [00:41<00:00,  2.31it/s]
Epoch-0, seen -- Mean err: 4.15, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 0: 2.09 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 6061/6061 [06:06<00:00, 16.52it/s]
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 113/113 [00:49<00:00,  2.30it/s]
Epoch-0, unseen -- Mean err: 5.25, Acc: 0.98, Rec : 1.00, Class and Pose  : 0.98
Validation time for epoch 0: 6.85 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 6649/6649 [06:51<00:00, 16.17it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:16<00:00,  2.26it/s]
Epoch-0, seen_occ -- Mean err: 11.88, Acc: 0.85, Rec : 0.92, Class and Pose  : 0.85
Validation time for epoch 0: 7.71 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 1688/1688 [01:17<00:00, 21.80it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 554/554 [53:33<00:00,  5.80s/it]
Epoch-0, unseen_occ -- Mean err: 14.46, Acc: 0.78, Rec : 1.00, Class and Pose  : 0.78
Validation time for epoch 0: 1.60 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 151/151 [01:05<00:00,  2.32it/s]1s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 872/872 [00:44<00:00, 19.49it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 95/95 [00:41<00:00,  2.32it/s]
Epoch-1, seen -- Mean err: 4.30, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 1: 1.86 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 6061/6061 [04:52<00:00, 20.75it/s]
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 113/113 [00:48<00:00,  2.32it/s]
Epoch-1, unseen -- Mean err: 5.21, Acc: 0.98, Rec : 1.00, Class and Pose  : 0.98
Validation time for epoch 1: 5.59 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 6649/6649 [05:21<00:00, 20.65it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:16<00:00,  2.27it/s]
Epoch-1, seen_occ -- Mean err: 13.20, Acc: 0.83, Rec : 0.89, Class and Pose  : 0.83
Validation time for epoch 1: 6.21 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 1688/1688 [01:19<00:00, 21.30it/s]
  8%|██████████                                                                                                                   | 2/25 [2:21:29<26:59:49, 4225.62s/it]
Epoch-1, unseen_occ -- Mean err: 14.77, Acc: 0.78, Rec : 1.00, Class and Pose  : 0.78
Validation time for epoch 1: 1.64 minutes
 25%|████████████████████████████████▊                                                                                                | 141/554 [13:40<39:49,  5.79s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
