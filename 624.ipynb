#%%
import torch
import time

# 测试单个文件加载时间
dino_path = '/data1/dataset/renjl/LINEMOD/ape/000123_dino.pt'
sam_path = '/data1/dataset/renjl/LINEMOD/ape/000123_sam.pt'

print('测试单个特征文件加载时间...')

# 测试DINO加载
start = time.time()
dino_feat = torch.load(dino_path)
dino_time = time.time() - start
print(f'DINO加载时间: {dino_time:.3f}秒, 形状: {dino_feat.shape}')

# 测试SAM加载  
start = time.time()
sam_feat = torch.load(sam_path)
sam_time = time.time() - start
print(f'SAM加载时间: {sam_time:.3f}秒, 形状: {sam_feat.shape}')

print(f'总加载时间: {dino_time + sam_time:.3f}秒')
#%%
import torch
import time

# 测试单个文件加载时间
dino_path = '../000123_dino.pt'
sam_path = '../000123_sam.pt'

print('测试单个特征文件加载时间...')

# 测试DINO加载
start = time.time()
dino_feat = torch.load(dino_path)
dino_time = time.time() - start
print(f'DINO加载时间: {dino_time:.3f}秒, 形状: {dino_feat.shape}')

# 测试SAM加载  
start = time.time()
sam_feat = torch.load(sam_path)
sam_time = time.time() - start
print(f'SAM加载时间: {sam_time:.3f}秒, 形状: {sam_feat.shape}')

print(f'总加载时间: {dino_time + sam_time:.3f}秒')