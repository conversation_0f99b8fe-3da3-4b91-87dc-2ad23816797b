###  Re-implementation of VSD metric from [BOP toolkit](https://github.com/thodan/bop_toolkit)
This implementation is taken from three files: [pose_error.py](https://github.com/thodan/bop_toolkit/blob/master/bop_toolkit_lib/pose_error.py), [visibility.py](https://github.com/thodan/bop_toolkit/blob/master/bop_toolkit_lib/visibility.py) and [misc.py](https://github.com/thodan/bop_toolkit/blob/master/bop_toolkit_lib/misc.py). The only difference is the headless system renderer from Pyrender.


