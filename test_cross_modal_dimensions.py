#!/usr/bin/env python3
"""
Test Cross-Modal aggregation dimensions
"""

import torch
import torch.nn.functional as F
from lib.models.simple_dino_feature_network import CrossModalDINO_SAM_Aggregation

def test_cross_modal_dimensions():
    """测试Cross-Modal聚合网络的维度处理"""
    print("🧪 Testing Cross-Modal Aggregation Dimensions...")
    
    # 参数设置
    feature_dim = 1024  # DINO特征维度
    sam_dim = 192       # SAM特征维度
    descriptor_size = 128
    device = "cuda" if torch.cuda.is_available() else "cpu"
    
    print(f"📊 DINO feature dim: {feature_dim}")
    print(f"📊 SAM feature dim: {sam_dim}")
    print(f"📊 Descriptor size: {descriptor_size}")
    print(f"📊 Device: {device}")
    
    # 初始化聚合网络
    aggregation = CrossModalDINO_SAM_Aggregation(
        feature_dim=feature_dim,
        sam_dim=sam_dim,
        descriptor_size=descriptor_size,
        device=device
    )
    
    # 准备测试数据
    batch_size = 2
    num_layers = 4
    spatial_size = 16
    
    print(f"\n📊 Test data:")
    print(f"   Batch size: {batch_size}")
    print(f"   Num layers: {num_layers}")
    print(f"   Spatial size: {spatial_size}x{spatial_size}")
    
    # 模拟DINO特征
    cls_tokens = [torch.randn(batch_size, feature_dim, 1).to(device) for _ in range(num_layers)]
    patch_tokens = [torch.randn(batch_size, feature_dim, spatial_size**2).to(device) for _ in range(num_layers)]
    
    # 模拟SAM特征
    sam_cls_tokens = [torch.randn(batch_size, sam_dim, 1).to(device) for _ in range(num_layers)]
    sam_patch_tokens = [torch.randn(batch_size, sam_dim, spatial_size**2).to(device) for _ in range(num_layers)]
    
    print(f"\n📊 Input shapes:")
    print(f"   DINO CLS: {cls_tokens[0].shape}")
    print(f"   DINO Patch: {patch_tokens[0].shape}")
    print(f"   SAM CLS: {sam_cls_tokens[0].shape}")
    print(f"   SAM Patch: {sam_patch_tokens[0].shape}")
    
    # 测试前向传播
    try:
        print(f"\n🚀 Running forward pass...")
        with torch.no_grad():
            output = aggregation(
                cls_tokens=cls_tokens,
                patch_tokens=patch_tokens,
                sam_cls_tokens=sam_cls_tokens,
                sam_patch_tokens=sam_patch_tokens
            )
        
        print(f"\n✅ Forward pass successful!")
        print(f"📊 Output shapes:")
        print(f"   CLS feature: {output['cls_feature'].shape}")
        print(f"   Pose feature: {output['pose_feature'].shape}")
        print(f"   Pose pooled: {output['pose_pooled'].shape}")
        
        # 验证维度
        assert output['cls_feature'].shape == (batch_size, descriptor_size), \
            f"CLS feature shape mismatch: {output['cls_feature'].shape} vs ({batch_size}, {descriptor_size})"
        assert output['pose_feature'].shape[0] == batch_size, \
            f"Pose feature batch size mismatch: {output['pose_feature'].shape[0]} vs {batch_size}"
        assert output['pose_feature'].shape[1] == descriptor_size, \
            f"Pose feature descriptor size mismatch: {output['pose_feature'].shape[1]} vs {descriptor_size}"
        assert output['pose_pooled'].shape == (batch_size, descriptor_size), \
            f"Pose pooled shape mismatch: {output['pose_pooled'].shape} vs ({batch_size}, {descriptor_size})"
        
        print(f"\n🎉 All dimension tests passed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Forward pass failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        success = test_cross_modal_dimensions()
        if success:
            print("\n🎉 Cross-Modal dimension test completed successfully!")
        else:
            print("\n❌ Cross-Modal dimension test failed!")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
