import argparse
import os
import torch
import numpy as np
from tqdm import tqdm
import datetime
import torch.nn as nn
import logging
from tabulate import tabulate
import wandb

from lib.utils import weights, metrics, logger
from lib.utils.optimizer import adjust_learning_rate
from lib.datasets.dataloader_utils import init_dataloader
from lib.utils.config import Config

from lib.models.dino_network import DINOv2Extractor
from lib.models.dinov2.aggregation_network import AggregationNetwork
from lib.models.dino_feature_network import DINOv2FeatureExtractor
from lib.datasets.tless.dataloader_dino import TlessDino, TemplatesTlessDino
from lib.datasets.im_transform import im_transform, tensor2im
from lib.datasets.tless import inout
from lib.datasets.tless import training_utils_dino
from lib.datasets.tless import testing_utils_dino
import shutup
shutup.please()

# 设置CUDA设备
os.environ['CUDA_VISIBLE_DEVICES'] = '3'

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--config_path', type=str, default='config_run/TLESS_DINO.json')
    parser.add_argument('--use_wandb', action='store_true')
    parser.add_argument('--wandb_name', type=str, help='指定运行的名称，如果不指定则使用默认格式：配置文件名_时间戳')
    parser.add_argument('--checkpoint', type=str, default=None, help='加载预训练模型的路径')
    parser.add_argument('--result_path', type=str, default='./dataset/results/vis/tless')

    args = parser.parse_args()

    dir_name = (args.config_path.split('/')[-1]).split('.')[0]
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    run_name = f"{dir_name}_{timestamp}"
    print("config", run_name)

    # 生成运行名称
    if args.wandb_name:
        wandb_name = args.wandb_name
    else:
        wandb_name = f"{dir_name}_{timestamp}"

    # 设置日志目录
    log_dir = os.path.join('logs', run_name)
    os.makedirs(log_dir, exist_ok=True)

    # 加载配置
    config_global = Config(config_file="./config.json").get_config()
    config_run = Config(args.config_path).get_config()

    # 初始化日志
    save_path = os.path.join(config_global.root_path, config_run.log.weights, run_name)
    trainer_dir = os.path.join(os.getcwd(), "logs")
    trainer_logger = logger.init_logger(save_path=save_path,
                                    trainer_dir=trainer_dir,
                                    trainer_logger_name=run_name)

    # 初始化模型
    dino_extractor = DINOv2Extractor(
        device="cuda",
        output_resolution=config_run.model.output_resolution,
        pose_tokens_config=config_run.model.pose_tokens,
        feature_blocks_config=config_run.model.feature_blocks,
        version=config_run.model.version
    )

    # 获取特征维度
    feature_dim = dino_extractor.feature_dim
    num_blocks = len(config_run.model.feature_blocks['indices'])
    feature_dims = [feature_dim] * num_blocks

    # 初始化聚合网络
    aggregation_network = AggregationNetwork(
        descriptor_size=config_run.model.descriptor_size,
        feature_dims=feature_dims,
        device="cuda",
        input_dim=feature_dim,
        use_pose_tokens=config_run.model.pose_tokens.get('use_attention', False),
        use_residual=config_run.model.get('use_residual', True),
        use_cad_feature=config_run.model.cad_feature.get('enabled', False)
    )
    model = DINOv2FeatureExtractor(
        config=config_run,
        threshold=0.2,
        dino_extractor=dino_extractor,
        aggregation_network=aggregation_network,
    ).cuda()

    if args.checkpoint is not None:
        print("Loading checkpoint from:", args.checkpoint)
        checkpoint = torch.load(args.checkpoint)
        if 'model' in checkpoint:
            model.aggregation_network.load_state_dict(checkpoint['model'])
        if 'learnable_tokens' in checkpoint and model.dino_extractor.use_pose_tokens:
            model.dino_extractor.learnable_tokens = checkpoint['learnable_tokens']

    # 加载数据
    transforms = im_transform()
    seen_ids, unseen_ids = range(1, 5), range(15, 20)

    # 定义数据加载器配置
    config_loader = []
    # 训练数据：所有seen物体使用一个dataloader
    config_loader.append(["train", "train", "query", list(range(1,19))])
    # 测试数据：seen和unseen分别使用一个loader
    config_loader.append(["test", "seen_test", "query", list(seen_ids)])
    config_loader.append(["test", "seen_template", "template", list(seen_ids)])
    config_loader.append(["test", "unseen_test", "query", list(unseen_ids)])
    config_loader.append(["test", "unseen_template", "template", list(unseen_ids)])

    # 创建所有数据集
    datasetLoader = {}
    for config in config_loader:
        print(f"Dataset: {config[0]}, Name: {config[1]}, Type: {config[2]}, Objects: {config[3]}")
        save_sample_path = os.path.join(config_global.root_path,
                                    config_run.dataset.sample_path, dir_name, config[1])
        if config[2] == "template":
            loader = TemplatesTlessDino(
                root_dir=config_global.root_path,
                id_obj=config[3],  # id列表
                image_size=config_run.dataset.image_size,
                save_path=save_sample_path,
                im_transform=transforms
            )
        else:  # query
            loader = TlessDino(
                root_dir=config_global.root_path,
                dataset="tless",
                list_id_obj=config[3],  # id列表
                split=config[0],
                image_size=config_run.dataset.image_size,
                save_path=save_sample_path,
                use_normalize=True,
                use_mask=config_run.dataset.use_mask,
                gt_bbox_known=config_run.dataset.get('gt_bbox_known', True),
                use_aug=config_run.dataset.get('use_aug', False),
                aug_config=config_run.dataset.get('aug', None),
                num_negatives=config_run.dataset.get('num_negatives', 1)
            )
        datasetLoader[config[1]] = loader
        print("---" * 20)

    # 使用init_dataloader初始化所有数据加载器
    datasetLoader = init_dataloader(
        dict_dataloader=datasetLoader,
        batch_size=config_run.train.batch_size,
        num_workers=config_run.train.num_workers
    )

    # 初始化优化器
    optimizer = torch.optim.Adam(
        list(model.parameters()),
        lr=config_run.train.optimizer.lr,
        weight_decay=config_run.train.optimizer.weight_decay)

    # 初始化wandb
    if args.use_wandb:
        wandb.init(
            project="pose-estimation-tless",
            name=wandb_name,
            config={
                "learning_rate": config_run.train.optimizer.lr,
                "batch_size": config_run.train.batch_size,
                "epochs": config_run.train.epochs,
                "weight_decay": config_run.train.optimizer.weight_decay,
                "descriptor_size": config_run.model.descriptor_size,
                "use_residual": config_run.model.get('use_residual', True),
                "use_pose_tokens": config_run.model.pose_tokens.get('use_attention', False)
            }
        )

    # 在训练开始前打印可训练参数数量
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    trainable_params_M = trainable_params / 1e6
    print(f"可训练参数数量: {trainable_params_M:.2f}M")

    # 训练循环
    for epoch in tqdm(range(0, config_run.train.epochs)):
        # 更新学习率
        if epoch in config_run.train.scheduler.milestones:
            adjust_learning_rate(optimizer, config_run.train.optimizer.lr, config_run.train.scheduler.gamma)

        # 训练一个epoch
        train_loss = training_utils_dino.train(
            train_data=datasetLoader["train"],
            model=model,
            optimizer=optimizer,
            warm_up_config=[1000, config_run.train.optimizer.lr],
            epoch=epoch,
            logger=trainer_logger,
            log_interval=config_run.log.log_interval,
            regress_delta=config_run.model.regression_loss,
            use_wandb=args.use_wandb
        )

        # 每5个epoch进行测试
        if (epoch + 1) % 1 == 0:
            trainer_logger.info(f"Testing at epoch {epoch + 1}")

            # 准备表格数据
            metrics = ["error", "accuracy", "recognition", "recognition and pose"]
            headers = ["Object"] + metrics
            table_data = []

            # 分别测试seen和unseen物体
            for config_split in [["seen", seen_ids], ["unseen", unseen_ids]]:
                split_name = config_split[0]
                split_ids = config_split[1]
                query_name = f"{split_name}_test"
                template_name = f"{split_name}_template"

                print(f"\nTesting {split_name} objects...")
                
                # 进行测试
                list_err, list_pose_acc, list_class_acc, list_class_and_pose_acc15 = testing_utils_dino.test(
                    query_data=datasetLoader[query_name],
                    template_data=datasetLoader[template_name],
                    model=model,
                    epoch=epoch,
                    logger=trainer_logger,
                    save_prediction_path=None,
                    gt_bbox_known=config_run.dataset.get('gt_bbox_known', True)
                )

                # 计算该split的平均值
                split_mean_err = np.mean([list_err[i].item() for i in split_ids])
                split_mean_pose_acc = np.mean([list_pose_acc[i].item() for i in split_ids])
                split_mean_class_acc = np.mean([list_class_acc[i].item() for i in split_ids])
                split_mean_class_and_pose_acc = np.mean([list_class_and_pose_acc15[i].item() for i in split_ids])

                # 记录到wandb
                if args.use_wandb:
                    wandb.log({
                        f"{split_name}/mean_error": split_mean_err,
                        f"{split_name}/mean_pose_accuracy": split_mean_pose_acc,
                        "epoch": epoch + 1
                    })

                # 添加该split的mean行
                mean_row = [f"Mean ({split_name.capitalize()})"]
                mean_row.extend([f"{split_mean_err:.4f}", f"{split_mean_pose_acc:.4f}", 
                               f"{split_mean_class_acc:.4f}", f"{split_mean_class_and_pose_acc:.4f}"])
                table_data.append(mean_row)

                # 添加分隔行
                table_data.append(["-" * 10] + ["-" * 8] * len(metrics))

                # 添加该split的每个物体的行
                for id_obj in split_ids:
                    row = [f"Object {id_obj:02d}"]
                    row.append(f"{list_err[id_obj].item():.4f}")
                    row.append(f"{list_pose_acc[id_obj].item():.4f}")
                    row.append(f"{list_class_acc[id_obj].item():.4f}")
                    row.append(f"{list_class_and_pose_acc15[id_obj].item():.4f}")
                    table_data.append(row)

                # 添加分隔行
                table_data.append(["-" * 10] + ["-" * 8] * len(metrics))

            # 打印表格
            print("\nTest Results:")
            print("=" * 50)
            print(tabulate(table_data, headers=headers, tablefmt="grid"))
            print("=" * 50)

            # 保存模型
            save_file = os.path.join(save_path, 'model_epoch{}.pth'.format(epoch))
            trainer_logger.info("Saving to {}".format(save_file))
            os.makedirs(os.path.dirname(save_file), exist_ok=True)
            
            # 准备保存的状态字典
            state_dict = {
                'model': model.aggregation_network.state_dict(),
                'optimizer': optimizer.state_dict(),
                'epoch': epoch,
                'train_loss': train_loss
            }
            
            if model.dino_extractor.use_pose_tokens:
                state_dict['learnable_tokens'] = model.dino_extractor.learnable_tokens
            
            torch.save(state_dict, save_file)

if __name__ == "__main__":
    main() 