"""
测试逐点独立哈希功能
验证空间哈希是否正确保持空间信息
"""

import torch
import numpy as np
from lib.models.spatial_hash import AdaptiveHashEncoder, SpatialHashSimilarity


def test_spatial_hash_basic():
    """测试基本的空间哈希功能"""
    print("=== 测试逐点独立哈希基本功能 ===\n")
    
    # 测试参数
    batch_size = 2
    input_dim = 256
    hash_bits = 64
    H, W = 16, 16
    
    # 创建哈希编码器
    hash_encoder = AdaptiveHashEncoder(
        input_dim=input_dim,
        hash_bits=hash_bits,
        use_attention=True,
        dropout_rate=0.1
    )
    
    print(f"1. 测试向量特征哈希...")
    # 测试向量特征
    vector_input = torch.randn(batch_size, input_dim)
    vector_hash = hash_encoder(vector_input)
    
    print(f"   输入向量特征: {vector_input.shape}")
    print(f"   输出向量哈希: {vector_hash.shape}")
    print(f"   期望形状: [{batch_size}, {hash_bits}]")
    assert vector_hash.shape == (batch_size, hash_bits), f"向量哈希形状错误: {vector_hash.shape}"
    print("   ✅ 向量哈希测试通过")
    
    print(f"\n2. 测试空间特征哈希...")
    # 测试空间特征
    spatial_input = torch.randn(batch_size, input_dim, H, W)
    spatial_hash = hash_encoder(spatial_input)
    
    print(f"   输入空间特征: {spatial_input.shape}")
    print(f"   输出空间哈希: {spatial_hash.shape}")
    print(f"   期望形状: [{batch_size}, {hash_bits}, {H}, {W}]")
    assert spatial_hash.shape == (batch_size, hash_bits, H, W), f"空间哈希形状错误: {spatial_hash.shape}"
    print("   ✅ 空间哈希测试通过")
    
    return vector_hash, spatial_hash


def test_spatial_similarity():
    """测试空间哈希相似度计算"""
    print("\n=== 测试空间哈希相似度计算 ===\n")
    
    # 测试参数
    batch_size = 3
    num_templates = 5
    hash_bits = 64
    H, W = 16, 16
    
    # 生成测试数据
    query_hash = torch.randn(batch_size, hash_bits, H, W)
    template_hash = torch.randn(num_templates, hash_bits, H, W)
    
    calculator = SpatialHashSimilarity()
    
    print(f"1. 测试训练模式（余弦相似度）...")
    cosine_sim = calculator.spatial_cosine_similarity(query_hash, template_hash)
    print(f"   查询哈希: {query_hash.shape}")
    print(f"   模板哈希: {template_hash.shape}")
    print(f"   余弦相似度: {cosine_sim.shape}")
    print(f"   相似度范围: [{cosine_sim.min():.3f}, {cosine_sim.max():.3f}]")
    assert cosine_sim.shape == (batch_size, num_templates), f"余弦相似度形状错误: {cosine_sim.shape}"
    print("   ✅ 余弦相似度测试通过")
    
    print(f"\n2. 测试推理模式（汉明相似度）...")
    hamming_sim = calculator.spatial_hamming_similarity(query_hash, template_hash)
    print(f"   汉明相似度: {hamming_sim.shape}")
    print(f"   相似度范围: [{hamming_sim.min():.3f}, {hamming_sim.max():.3f}]")
    assert hamming_sim.shape == (batch_size, num_templates), f"汉明相似度形状错误: {hamming_sim.shape}"
    print("   ✅ 汉明相似度测试通过")
    
    print(f"\n3. 测试自适应相似度...")
    adaptive_sim_train = calculator.adaptive_similarity(query_hash, template_hash, training=True)
    adaptive_sim_eval = calculator.adaptive_similarity(query_hash, template_hash, training=False)
    
    print(f"   自适应相似度(训练): {adaptive_sim_train.shape}")
    print(f"   自适应相似度(推理): {adaptive_sim_eval.shape}")
    
    # 验证自适应相似度与对应模式的相似度一致
    assert torch.allclose(adaptive_sim_train, cosine_sim, atol=1e-6), "训练模式自适应相似度不一致"
    assert torch.allclose(adaptive_sim_eval, hamming_sim, atol=1e-6), "推理模式自适应相似度不一致"
    print("   ✅ 自适应相似度测试通过")
    
    return cosine_sim, hamming_sim


def test_spatial_vs_global():
    """对比空间哈希与全局哈希的差异"""
    print("\n=== 对比空间哈希 vs 全局哈希 ===\n")
    
    # 测试参数
    batch_size = 2
    input_dim = 256
    hash_bits = 64
    H, W = 16, 16
    
    # 创建哈希编码器
    hash_encoder = AdaptiveHashEncoder(input_dim, hash_bits)
    
    # 生成空间特征
    spatial_input = torch.randn(batch_size, input_dim, H, W)
    
    print(f"1. 空间哈希方法...")
    # 方法1: 直接空间哈希
    spatial_hash = hash_encoder(spatial_input)  # [B, hash_bits, H, W]
    print(f"   空间哈希输出: {spatial_hash.shape}")
    
    print(f"\n2. 全局哈希方法...")
    # 方法2: 先池化再哈希
    global_pooled = torch.nn.functional.adaptive_avg_pool2d(spatial_input, (1, 1)).squeeze(-1).squeeze(-1)
    global_hash = hash_encoder(global_pooled)  # [B, hash_bits]
    print(f"   全局池化特征: {global_pooled.shape}")
    print(f"   全局哈希输出: {global_hash.shape}")
    
    print(f"\n3. 信息保持对比...")
    # 计算空间哈希的全局平均（模拟全局信息）
    spatial_global_avg = spatial_hash.mean(dim=(2, 3))  # [B, hash_bits]
    print(f"   空间哈希全局平均: {spatial_global_avg.shape}")
    
    # 计算差异
    diff = torch.norm(spatial_global_avg - global_hash, dim=1).mean()
    print(f"   全局信息差异: {diff:.6f}")
    
    # 计算空间变异性（空间哈希的优势）
    spatial_variance = spatial_hash.var(dim=(2, 3)).mean()
    print(f"   空间变异性: {spatial_variance:.6f}")
    
    print(f"\n4. 空间信息保持验证...")
    # 验证不同空间位置的哈希码确实不同
    pos1_hash = spatial_hash[:, :, 0, 0]  # 左上角
    pos2_hash = spatial_hash[:, :, H//2, W//2]  # 中心
    pos3_hash = spatial_hash[:, :, H-1, W-1]  # 右下角
    
    diff_12 = torch.norm(pos1_hash - pos2_hash, dim=1).mean()
    diff_13 = torch.norm(pos1_hash - pos3_hash, dim=1).mean()
    diff_23 = torch.norm(pos2_hash - pos3_hash, dim=1).mean()
    
    print(f"   位置(0,0)与中心差异: {diff_12:.6f}")
    print(f"   位置(0,0)与右下差异: {diff_13:.6f}")
    print(f"   中心与右下差异: {diff_23:.6f}")
    
    if diff_12 > 0.1 or diff_13 > 0.1 or diff_23 > 0.1:
        print("   ✅ 空间位置确实产生了不同的哈希码")
    else:
        print("   ⚠️  空间位置的哈希码差异较小")
    
    return spatial_hash, global_hash


def test_training_vs_inference():
    """测试训练模式与推理模式的差异"""
    print("\n=== 测试训练模式 vs 推理模式 ===\n")
    
    # 测试参数
    batch_size = 2
    input_dim = 256
    hash_bits = 64
    H, W = 16, 16
    
    # 创建哈希编码器
    hash_encoder = AdaptiveHashEncoder(input_dim, hash_bits)
    spatial_input = torch.randn(batch_size, input_dim, H, W)
    
    print(f"1. 训练模式...")
    hash_encoder.train()
    with torch.no_grad():
        train_hash = hash_encoder(spatial_input)
    
    print(f"   训练模式哈希范围: [{train_hash.min():.3f}, {train_hash.max():.3f}]")
    train_binary_ratio = ((torch.abs(train_hash) > 0.9).float().mean()).item()
    print(f"   训练模式二值化程度: {train_binary_ratio:.3f}")
    
    print(f"\n2. 推理模式...")
    hash_encoder.eval()
    with torch.no_grad():
        eval_hash = hash_encoder(spatial_input)
    
    print(f"   推理模式哈希范围: [{eval_hash.min():.3f}, {eval_hash.max():.3f}]")
    eval_binary_ratio = ((torch.abs(eval_hash) > 0.9).float().mean()).item()
    print(f"   推理模式二值化程度: {eval_binary_ratio:.3f}")
    
    # 验证推理模式确实进行了二值化
    unique_values = torch.unique(eval_hash)
    print(f"   推理模式唯一值数量: {len(unique_values)}")
    print(f"   推理模式唯一值: {unique_values[:10].tolist()}")  # 显示前10个
    
    if len(unique_values) <= 2:
        print("   ✅ 推理模式正确进行了二值化")
    else:
        print("   ⚠️  推理模式未完全二值化")
    
    return train_hash, eval_hash


def main():
    """主测试函数"""
    print("逐点独立哈希功能测试")
    print("=" * 50)
    
    try:
        # 基本功能测试
        vector_hash, spatial_hash = test_spatial_hash_basic()
        
        # 相似度计算测试
        cosine_sim, hamming_sim = test_spatial_similarity()
        
        # 空间vs全局对比
        spatial_hash_result, global_hash_result = test_spatial_vs_global()
        
        # 训练vs推理模式
        train_hash, eval_hash = test_training_vs_inference()
        
        print("\n" + "=" * 50)
        print("🎉 所有测试通过！")
        print("\n逐点独立哈希功能特点:")
        print("✅ 完全保持空间结构")
        print("✅ 每个空间位置独立编码")
        print("✅ 训练时连续值，推理时二值化")
        print("✅ 支持空间相似度计算")
        print("\n可以在配置文件中启用哈希功能进行训练！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
